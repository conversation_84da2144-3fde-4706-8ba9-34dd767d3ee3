{"version": 3, "file": "firebase-messaging-compat.js", "sources": ["../../node_modules/idb/build/index.js", "../util/src/environment.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../installations/src/util/constants.ts", "../messaging/src/interfaces/internal-message-payload.ts", "../messaging/src/util/constants.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../messaging/src/helpers/array-base64-translator.ts", "../messaging/src/helpers/migrate-old-database.ts", "../messaging/src/internals/idb-manager.ts", "../messaging/src/util/errors.ts", "../messaging/src/internals/requests.ts", "../messaging/src/internals/token-manager.ts", "../messaging/src/helpers/externalizePayload.ts", "../messaging/src/helpers/logToFirelog.ts", "../messaging/src/helpers/extract-app-config.ts", "../messaging/src/messaging-service.ts", "../messaging/src/helpers/registerDefaultSw.ts", "../messaging/src/api/getToken.ts", "../messaging/src/helpers/updateVapidKey.ts", "../messaging/src/helpers/updateSwReg.ts", "../messaging/src/helpers/logToScion.ts", "../messaging/src/listeners/window-listener.ts", "../messaging/src/helpers/is-console-message.ts", "../messaging/src/helpers/register.ts", "../messaging/src/api.ts", "../messaging/src/api/deleteToken.ts", "../messaging/src/api/onMessage.ts", "../messaging/src/listeners/sw-listeners.ts", "../messaging/src/helpers/sleep.ts", "../messaging/src/api/onBackgroundMessage.ts", "../messaging-compat/src/messaging-compat.ts", "../messaging-compat/src/registerMessagingCompat.ts", "../messaging-compat/src/index.ts"], "sourcesContent": ["import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n * in compliance with the License. You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under the License\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n * or implied. See the License for the specific language governing permissions and limitations under\n * the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ANALYTICS_ENABLED,\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\n\nexport interface MessagePayloadInternal {\n  notification?: NotificationPayloadInternal;\n  data?: unknown;\n  fcmOptions?: FcmOptionsInternal;\n  messageType?: MessageType;\n  isFirebaseMessaging?: boolean;\n  from: string;\n  fcmMessageId: string;\n  productId: number;\n  // eslint-disable-next-line camelcase\n  collapse_key: string;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Notification/actions\ninterface NotificationAction {\n  action: string;\n  icon?: string;\n  title: string;\n}\n\n/**\n * This interface defines experimental properties of NotificationOptions, that are not part of\n * the interface in the generated DOM types at https://github.com/microsoft/TypeScript-DOM-lib-generator/blob/179bdd84a944933a3103f29c2274c9f5a857b693/baselines/dom.generated.d.ts#L1012\n * https://developer.mozilla.org/en-US/docs/Web/API/Notification\n */\ninterface NotificationOptionsExperimental extends NotificationOptions {\n  readonly maxActions?: number;\n  readonly actions?: NotificationAction[];\n  readonly image?: string;\n  readonly renotify?: boolean;\n  readonly timestamp?: EpochTimeStamp;\n  readonly vibrate?: VibratePattern;\n}\n\nexport interface NotificationPayloadInternal\n  extends NotificationOptionsExperimental {\n  title: string;\n  // Supported in the Legacy Send API.\n  // See:https://firebase.google.com/docs/cloud-messaging/xmpp-server-ref.\n  // eslint-disable-next-line camelcase\n  click_action?: string;\n  icon?: string;\n}\n\n// Defined in\n// https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#webpushfcmoptions. Note\n// that the keys are sent to the clients in snake cases which we need to convert to camel so it can\n// be exposed as a type to match the Firebase API convention.\nexport interface FcmOptionsInternal {\n  link?: string;\n\n  // eslint-disable-next-line camelcase\n  analytics_label?: string;\n}\n\nexport enum MessageType {\n  PUSH_RECEIVED = 'push-received',\n  NOTIFICATION_CLICKED = 'notification-clicked'\n}\n\n/** Additional data of a message sent from the FN Console. */\nexport interface ConsoleMessageData {\n  [CONSOLE_CAMPAIGN_ID]: string;\n  [CONSOLE_CAMPAIGN_TIME]: string;\n  [CONSOLE_CAMPAIGN_NAME]?: string;\n  [CONSOLE_CAMPAIGN_ANALYTICS_ENABLED]?: '1';\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_SW_PATH = '/firebase-messaging-sw.js';\nexport const DEFAULT_SW_SCOPE = '/firebase-cloud-messaging-push-scope';\n\nexport const DEFAULT_VAPID_KEY =\n  'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\n\nexport const ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\n\n/** Key of FCM Payload in Notification's data field. */\nexport const FCM_MSG = 'FCM_MSG';\n\nexport const CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\nexport const CONSOLE_CAMPAIGN_NAME = 'google.c.a.c_l';\nexport const CONSOLE_CAMPAIGN_TIME = 'google.c.a.ts';\n/** Set to '1' if Analytics is enabled for the campaign */\nexport const CONSOLE_CAMPAIGN_ANALYTICS_ENABLED = 'google.c.a.e';\nexport const TAG = 'FirebaseMessaging: ';\nexport const MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST = 1000;\nexport const MAX_RETRIES = 3;\nexport const LOG_INTERVAL_IN_MS = 86400000; //24 hour\nexport const DEFAULT_BACKOFF_TIME_MS = 5000;\nexport const DEFAULT_REGISTRATION_TIMEOUT = 10000;\n\n// FCM log source name registered at Firelog: 'FCM_CLIENT_EVENT_LOGGING'. It uniquely identifies\n// FCM's logging configuration.\nexport const FCM_LOG_SOURCE = 1249;\n\n// Defined as in proto/messaging_event.proto. Neglecting fields that are supported.\nexport const SDK_PLATFORM_WEB = 3;\nexport const EVENT_MESSAGE_DELIVERED = 1;\n\nexport enum MessageType {\n  DATA_MESSAGE = 1,\n  DISPLAY_NOTIFICATION = 3\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function arrayToBase64(array: Uint8Array | ArrayBuffer): string {\n  const uint8Array = new Uint8Array(array);\n  const base64String = btoa(String.fromCharCode(...uint8Array));\n  return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\nexport function base64ToArray(base64String: string): Uint8Array {\n  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n  const base64 = (base64String + padding)\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n\n  const rawData = atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteDB, openDB } from 'idb';\n\nimport { TokenDetails } from '../interfaces/token-details';\nimport { arrayToBase64 } from './array-base64-translator';\n\n// https://github.com/firebase/firebase-js-sdk/blob/7857c212f944a2a9eb421fd4cb7370181bc034b5/packages/messaging/src/interfaces/token-details.ts\nexport interface V2TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: string | Uint8Array;\n  subscription: PushSubscription;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  createTime?: number;\n  endpoint?: string;\n  auth?: string;\n  p256dh?: string;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/6b5b15ce4ea3df5df5df8a8b33a4e41e249c7715/packages/messaging/src/interfaces/token-details.ts\nexport interface V3TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  endpoint: string;\n  auth: ArrayBuffer;\n  p256dh: ArrayBuffer;\n  createTime: number;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/9567dba664732f681fa7fe60f5b7032bb1daf4c9/packages/messaging/src/interfaces/token-details.ts\nexport interface V4TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  endpoint: string;\n  auth: ArrayBufferLike;\n  p256dh: ArrayBufferLike;\n  createTime: number;\n}\n\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\n * callback is called for all versions of the old DB.\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\n\nexport async function migrateOldDatabase(\n  senderId: string\n): Promise<TokenDetails | null> {\n  if ('databases' in indexedDB) {\n    // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n    // typecast when it lands in TS types.\n    const databases = await (\n      indexedDB as {\n        databases(): Promise<Array<{ name: string; version: number }>>;\n      }\n    ).databases();\n    const dbNames = databases.map(db => db.name);\n\n    if (!dbNames.includes(OLD_DB_NAME)) {\n      // old DB didn't exist, no need to open.\n      return null;\n    }\n  }\n\n  let tokenDetails: TokenDetails | null = null;\n\n  const db = await openDB(OLD_DB_NAME, OLD_DB_VERSION, {\n    upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\n      if (oldVersion < 2) {\n        // Database too old, skip migration.\n        return;\n      }\n\n      if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n        // Database did not exist. Nothing to do.\n        return;\n      }\n\n      const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n      const value = await objectStore.index('fcmSenderId').get(senderId);\n      await objectStore.clear();\n\n      if (!value) {\n        // No entry in the database, nothing to migrate.\n        return;\n      }\n\n      if (oldVersion === 2) {\n        const oldDetails = value as V2TokenDetails;\n\n        if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n          return;\n        }\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime ?? Date.now(),\n          subscriptionOptions: {\n            auth: oldDetails.auth,\n            p256dh: oldDetails.p256dh,\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey:\n              typeof oldDetails.vapidKey === 'string'\n                ? oldDetails.vapidKey\n                : arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 3) {\n        const oldDetails = value as V3TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 4) {\n        const oldDetails = value as V4TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      }\n    }\n  });\n  db.close();\n\n  // Delete all old databases.\n  await deleteDB(OLD_DB_NAME);\n  await deleteDB('fcm_vapid_details_db');\n  await deleteDB('undefined');\n\n  return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n}\n\nfunction checkTokenDetails(\n  tokenDetails: TokenDetails | null\n): tokenDetails is TokenDetails {\n  if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n    return false;\n  }\n  const { subscriptionOptions } = tokenDetails;\n  return (\n    typeof tokenDetails.createTime === 'number' &&\n    tokenDetails.createTime > 0 &&\n    typeof tokenDetails.token === 'string' &&\n    tokenDetails.token.length > 0 &&\n    typeof subscriptionOptions.auth === 'string' &&\n    subscriptionOptions.auth.length > 0 &&\n    typeof subscriptionOptions.p256dh === 'string' &&\n    subscriptionOptions.p256dh.length > 0 &&\n    typeof subscriptionOptions.endpoint === 'string' &&\n    subscriptionOptions.endpoint.length > 0 &&\n    typeof subscriptionOptions.swScope === 'string' &&\n    subscriptionOptions.swScope.length > 0 &&\n    typeof subscriptionOptions.vapidKey === 'string' &&\n    subscriptionOptions.vapidKey.length > 0\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, deleteDB, openDB } from 'idb';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { TokenDetails } from '../interfaces/token-details';\nimport { migrateOldDatabase } from '../helpers/migrate-old-database';\n\n// Exported for tests.\nexport const DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\n\ninterface MessagingDB extends DBSchema {\n  'firebase-messaging-store': {\n    key: string;\n    value: TokenDetails;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<MessagingDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<MessagingDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (upgradeDb, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n        // because if there are multiple versions between the old version and the current version, we\n        // want ALL the migrations that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function dbGet(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<TokenDetails | undefined> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tokenDetails = (await db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key)) as TokenDetails;\n\n  if (tokenDetails) {\n    return tokenDetails;\n  } else {\n    // Check if there is a tokenDetails object in the old DB.\n    const oldTokenDetails = await migrateOldDatabase(\n      firebaseDependencies.appConfig.senderId\n    );\n    if (oldTokenDetails) {\n      await dbSet(firebaseDependencies, oldTokenDetails);\n      return oldTokenDetails;\n    }\n  }\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function dbSet(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<TokenDetails> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n  await tx.done;\n  return tokenDetails;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function dbRemove(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<void> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/** Deletes the DB. Useful for tests. */\nexport async function dbDelete(): Promise<void> {\n  if (dbPromise) {\n    (await dbPromise).close();\n    await deleteDB(DATABASE_NAME);\n    dbPromise = null;\n  }\n}\n\nfunction getKey({ appConfig }: FirebaseInternalDependencies): string {\n  return appConfig.appId;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  AVAILABLE_IN_WINDOW = 'only-available-in-window',\n  AVAILABLE_IN_SW = 'only-available-in-sw',\n  PERMISSION_DEFAULT = 'permission-default',\n  PERMISSION_BLOCKED = 'permission-blocked',\n  UNSUPPORTED_BROWSER = 'unsupported-browser',\n  INDEXED_DB_UNSUPPORTED = 'indexed-db-unsupported',\n  FAILED_DEFAULT_REGISTRATION = 'failed-service-worker-registration',\n  TOKEN_SUBSCRIBE_FAILED = 'token-subscribe-failed',\n  TOKEN_SUBSCRIBE_NO_TOKEN = 'token-subscribe-no-token',\n  TOKEN_UNSUBSCRIBE_FAILED = 'token-unsubscribe-failed',\n  TOKEN_UPDATE_FAILED = 'token-update-failed',\n  TOKEN_UPDATE_NO_TOKEN = 'token-update-no-token',\n  INVALID_BG_HANDLER = 'invalid-bg-handler',\n  USE_SW_AFTER_GET_TOKEN = 'use-sw-after-get-token',\n  INVALID_SW_REGISTRATION = 'invalid-sw-registration',\n  USE_VAPID_KEY_AFTER_GET_TOKEN = 'use-vapid-key-after-get-token',\n  INVALID_VAPID_KEY = 'invalid-vapid-key'\n}\n\nexport const ERROR_MAP: ErrorMap<ErrorCode> = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.AVAILABLE_IN_WINDOW]:\n    'This method is available in a Window context.',\n  [ErrorCode.AVAILABLE_IN_SW]:\n    'This method is available in a service worker context.',\n  [ErrorCode.PERMISSION_DEFAULT]:\n    'The notification permission was not granted and dismissed instead.',\n  [ErrorCode.PERMISSION_BLOCKED]:\n    'The notification permission was not granted and blocked instead.',\n  [ErrorCode.UNSUPPORTED_BROWSER]:\n    \"This browser doesn't support the API's required to use the Firebase SDK.\",\n  [ErrorCode.INDEXED_DB_UNSUPPORTED]:\n    \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]:\n    'We are unable to register the default service worker. {$browserErrorMessage}',\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]:\n    'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN]:\n    'FCM returned no token when subscribing the user to push.',\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]:\n    'A problem occurred while unsubscribing the ' +\n    'user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_FAILED]:\n    'A problem occurred while updating the user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_NO_TOKEN]:\n    'FCM returned no token when updating the user to push.',\n  [ErrorCode.USE_SW_AFTER_GET_TOKEN]:\n    'The useServiceWorker() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your service worker is used.',\n  [ErrorCode.INVALID_SW_REGISTRATION]:\n    'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n  [ErrorCode.INVALID_BG_HANDLER]:\n    'The input to setBackgroundMessageHandler() must be a function.',\n  [ErrorCode.INVALID_VAPID_KEY]: 'The public VAPID key must be a string.',\n  [ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN]:\n    'The usePublicVapidKey() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your VAPID key is used.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]: { browserErrorMessage: string };\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UPDATE_FAILED]: { errorInfo: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'messaging',\n  'Messaging',\n  ERROR_MAP\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, ENDPOINT } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\n\nexport interface ApiResponse {\n  token?: string;\n  error?: { message: string };\n}\n\nexport interface ApiRequestBody {\n  web: {\n    endpoint: string;\n    p256dh: string;\n    auth: string;\n    applicationPubKey?: string;\n  };\n}\n\nexport async function requestGetToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(subscriptionOptions);\n\n  const subscribeOptions = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      getEndpoint(firebaseDependencies.appConfig),\n      subscribeOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestUpdateToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(tokenDetails.subscriptionOptions!);\n\n  const updateOptions = {\n    method: 'PATCH',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`,\n      updateOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestDeleteToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  token: string\n): Promise<void> {\n  const headers = await getHeaders(firebaseDependencies);\n\n  const unsubscribeOptions = {\n    method: 'DELETE',\n    headers\n  };\n\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${token}`,\n      unsubscribeOptions\n    );\n    const responseData: ApiResponse = await response.json();\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n        errorInfo: message\n      });\n    }\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n}\n\nfunction getEndpoint({ projectId }: AppConfig): string {\n  return `${ENDPOINT}/projects/${projectId!}/registrations`;\n}\n\nasync function getHeaders({\n  appConfig,\n  installations\n}: FirebaseInternalDependencies): Promise<Headers> {\n  const authToken = await installations.getToken();\n\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': appConfig.apiKey!,\n    'x-goog-firebase-installations-auth': `FIS ${authToken}`\n  });\n}\n\nfunction getBody({\n  p256dh,\n  auth,\n  endpoint,\n  vapidKey\n}: SubscriptionOptions): ApiRequestBody {\n  const body: ApiRequestBody = {\n    web: {\n      endpoint,\n      auth,\n      p256dh\n    }\n  };\n\n  if (vapidKey !== DEFAULT_VAPID_KEY) {\n    body.web.applicationPubKey = vapidKey;\n  }\n\n  return body;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\nimport {\n  arrayToBase64,\n  base64ToArray\n} from '../helpers/array-base64-translator';\nimport { dbGet, dbRemove, dbSet } from './idb-manager';\nimport {\n  requestDeleteToken,\n  requestGetToken,\n  requestUpdateToken\n} from './requests';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { MessagingService } from '../messaging-service';\n\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\n\nexport async function getTokenInternal(\n  messaging: MessagingService\n): Promise<string> {\n  const pushSubscription = await getPushSubscription(\n    messaging.swRegistration!,\n    messaging.vapidKey!\n  );\n\n  const subscriptionOptions: SubscriptionOptions = {\n    vapidKey: messaging.vapidKey!,\n    swScope: messaging.swRegistration!.scope,\n    endpoint: pushSubscription.endpoint,\n    auth: arrayToBase64(pushSubscription.getKey('auth')!),\n    p256dh: arrayToBase64(pushSubscription.getKey('p256dh')!)\n  };\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (!tokenDetails) {\n    // No token, get a new one.\n    return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n  } else if (\n    !isTokenValid(tokenDetails.subscriptionOptions!, subscriptionOptions)\n  ) {\n    // Invalid token, get a new one.\n    try {\n      await requestDeleteToken(\n        messaging.firebaseDependencies!,\n        tokenDetails.token\n      );\n    } catch (e) {\n      // Suppress errors because of #2364\n      console.warn(e);\n    }\n\n    return getNewToken(messaging.firebaseDependencies!, subscriptionOptions);\n  } else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n    // Weekly token refresh\n    return updateToken(messaging, {\n      token: tokenDetails.token,\n      createTime: Date.now(),\n      subscriptionOptions\n    });\n  } else {\n    // Valid token, nothing to do.\n    return tokenDetails.token;\n  }\n}\n\n/**\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\n * the push subscription if it exists.\n */\nexport async function deleteTokenInternal(\n  messaging: MessagingService\n): Promise<boolean> {\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (tokenDetails) {\n    await requestDeleteToken(\n      messaging.firebaseDependencies,\n      tokenDetails.token\n    );\n    await dbRemove(messaging.firebaseDependencies);\n  }\n\n  // Unsubscribe from the push subscription.\n  const pushSubscription =\n    await messaging.swRegistration!.pushManager.getSubscription();\n  if (pushSubscription) {\n    return pushSubscription.unsubscribe();\n  }\n\n  // If there's no SW, consider it a success.\n  return true;\n}\n\nasync function updateToken(\n  messaging: MessagingService,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  try {\n    const updatedToken = await requestUpdateToken(\n      messaging.firebaseDependencies,\n      tokenDetails\n    );\n\n    const updatedTokenDetails: TokenDetails = {\n      ...tokenDetails,\n      token: updatedToken,\n      createTime: Date.now()\n    };\n\n    await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n    return updatedToken;\n  } catch (e) {\n    throw e;\n  }\n}\n\nasync function getNewToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const token = await requestGetToken(\n    firebaseDependencies,\n    subscriptionOptions\n  );\n  const tokenDetails: TokenDetails = {\n    token,\n    createTime: Date.now(),\n    subscriptionOptions\n  };\n  await dbSet(firebaseDependencies, tokenDetails);\n  return tokenDetails.token;\n}\n\n/**\n * Gets a PushSubscription for the current user.\n */\nasync function getPushSubscription(\n  swRegistration: ServiceWorkerRegistration,\n  vapidKey: string\n): Promise<PushSubscription> {\n  const subscription = await swRegistration.pushManager.getSubscription();\n  if (subscription) {\n    return subscription;\n  }\n\n  return swRegistration.pushManager.subscribe({\n    userVisibleOnly: true,\n    // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n    // submitted to pushManager#subscribe must be of type Uint8Array.\n    applicationServerKey: base64ToArray(vapidKey)\n  });\n}\n\n/**\n * Checks if the saved tokenDetails object matches the configuration provided.\n */\nfunction isTokenValid(\n  dbOptions: SubscriptionOptions,\n  currentOptions: SubscriptionOptions\n): boolean {\n  const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n  const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n  const isAuthEqual = currentOptions.auth === dbOptions.auth;\n  const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n\n  return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MessagePayload } from '../interfaces/public-types';\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\n\nexport function externalizePayload(\n  internalPayload: MessagePayloadInternal\n): MessagePayload {\n  const payload: MessagePayload = {\n    from: internalPayload.from,\n    // eslint-disable-next-line camelcase\n    collapseKey: internalPayload.collapse_key,\n    // eslint-disable-next-line camelcase\n    messageId: internalPayload.fcmMessageId\n  } as MessagePayload;\n\n  propagateNotificationPayload(payload, internalPayload);\n  propagateDataPayload(payload, internalPayload);\n  propagateFcmOptions(payload, internalPayload);\n\n  return payload;\n}\n\nfunction propagateNotificationPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.notification) {\n    return;\n  }\n\n  payload.notification = {};\n\n  const title = messagePayloadInternal.notification!.title;\n  if (!!title) {\n    payload.notification!.title = title;\n  }\n\n  const body = messagePayloadInternal.notification!.body;\n  if (!!body) {\n    payload.notification!.body = body;\n  }\n\n  const image = messagePayloadInternal.notification!.image;\n  if (!!image) {\n    payload.notification!.image = image;\n  }\n\n  const icon = messagePayloadInternal.notification!.icon;\n  if (!!icon) {\n    payload.notification!.icon = icon;\n  }\n}\n\nfunction propagateDataPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.data) {\n    return;\n  }\n\n  payload.data = messagePayloadInternal.data as { [key: string]: string };\n}\n\nfunction propagateFcmOptions(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n  if (\n    !messagePayloadInternal.fcmOptions &&\n    !messagePayloadInternal.notification?.click_action\n  ) {\n    return;\n  }\n\n  payload.fcmOptions = {};\n\n  const link =\n    messagePayloadInternal.fcmOptions?.link ??\n    messagePayloadInternal.notification?.click_action;\n\n  if (!!link) {\n    payload.fcmOptions!.link = link;\n  }\n\n  // eslint-disable-next-line camelcase\n  const analyticsLabel = messagePayloadInternal.fcmOptions?.analytics_label;\n  if (!!analyticsLabel) {\n    payload.fcmOptions!.analyticsLabel = analyticsLabel;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_BACKOFF_TIME_MS,\n  EVENT_MESSAGE_DELIVERED,\n  FCM_LOG_SOURCE,\n  LOG_INTERVAL_IN_MS,\n  MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST,\n  MAX_RETRIES,\n  MessageType,\n  SDK_PLATFORM_WEB\n} from '../util/constants';\nimport {\n  FcmEvent,\n  LogEvent,\n  LogRequest,\n  LogResponse,\n  ComplianceData\n} from '../interfaces/logging-types';\n\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\nimport { MessagingService } from '../messaging-service';\n\nconst LOG_ENDPOINT = 'https://play.google.com/log?format=json_proto3';\n\nconst FCM_TRANSPORT_KEY = _mergeStrings(\n  'AzSCbw63g1R0nCw85jG8',\n  'Iaya3yLKwmgvh7cF0q4'\n);\n\nexport function startLoggingService(messaging: MessagingService): void {\n  if (!messaging.isLogServiceStarted) {\n    _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    messaging.isLogServiceStarted = true;\n  }\n}\n\n/**\n *\n * @param messaging the messaging instance.\n * @param offsetInMs this method execute after `offsetInMs` elapsed .\n */\nexport function _processQueue(\n  messaging: MessagingService,\n  offsetInMs: number\n): void {\n  setTimeout(async () => {\n    if (!messaging.deliveryMetricsExportedToBigQueryEnabled) {\n      // flush events and terminate logging service\n      messaging.logEvents = [];\n      messaging.isLogServiceStarted = false;\n\n      return;\n    }\n\n    if (!messaging.logEvents.length) {\n      return _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    }\n\n    await _dispatchLogEvents(messaging);\n  }, offsetInMs);\n}\n\nexport async function _dispatchLogEvents(\n  messaging: MessagingService\n): Promise<void> {\n  for (\n    let i = 0, n = messaging.logEvents.length;\n    i < n;\n    i += MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST\n  ) {\n    const logRequest = _createLogRequest(\n      messaging.logEvents.slice(i, i + MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST)\n    );\n\n    let retryCount = 0,\n      response = {} as Response;\n\n    do {\n      try {\n        response = await fetch(\n          LOG_ENDPOINT.concat('&key=', FCM_TRANSPORT_KEY),\n          {\n            method: 'POST',\n            body: JSON.stringify(logRequest)\n          }\n        );\n\n        // don't retry on 200s or non retriable errors\n        if (response.ok || (!response.ok && !isRetriableError(response))) {\n          break;\n        }\n\n        if (!response.ok && isRetriableError(response)) {\n          // rethrow to retry with quota\n          throw new Error(\n            'a retriable Non-200 code is returned in fetch to Firelog endpoint. Retry'\n          );\n        }\n      } catch (error) {\n        const isLastAttempt = retryCount === MAX_RETRIES;\n        if (isLastAttempt) {\n          // existing the do-while interactive retry logic because retry quota has reached.\n          break;\n        }\n      }\n\n      let delayInMs: number;\n      try {\n        delayInMs = Number(\n          ((await response.json()) as LogResponse).nextRequestWaitMillis\n        );\n      } catch (e) {\n        delayInMs = DEFAULT_BACKOFF_TIME_MS;\n      }\n\n      await new Promise(resolve => setTimeout(resolve, delayInMs));\n\n      retryCount++;\n    } while (retryCount < MAX_RETRIES);\n  }\n\n  messaging.logEvents = [];\n  // schedule for next logging\n  _processQueue(messaging, LOG_INTERVAL_IN_MS);\n}\n\nfunction isRetriableError(response: Response): boolean {\n  const httpStatus = response.status;\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\nexport async function stageLog(\n  messaging: MessagingService,\n  internalPayload: MessagePayloadInternal\n): Promise<void> {\n  const fcmEvent = createFcmEvent(\n    internalPayload,\n    await messaging.firebaseDependencies.installations.getId()\n  );\n\n  createAndEnqueueLogEvent(messaging, fcmEvent, internalPayload.productId);\n}\n\nfunction createFcmEvent(\n  internalPayload: MessagePayloadInternal,\n  fid: string\n): FcmEvent {\n  const fcmEvent = {} as FcmEvent;\n\n  /* eslint-disable camelcase */\n  // some fields should always be non-null. Still check to ensure.\n  if (!!internalPayload.from) {\n    fcmEvent.project_number = internalPayload.from;\n  }\n\n  if (!!internalPayload.fcmMessageId) {\n    fcmEvent.message_id = internalPayload.fcmMessageId;\n  }\n\n  fcmEvent.instance_id = fid;\n\n  if (!!internalPayload.notification) {\n    fcmEvent.message_type = MessageType.DISPLAY_NOTIFICATION.toString();\n  } else {\n    fcmEvent.message_type = MessageType.DATA_MESSAGE.toString();\n  }\n\n  fcmEvent.sdk_platform = SDK_PLATFORM_WEB.toString();\n  fcmEvent.package_name = self.origin.replace(/(^\\w+:|^)\\/\\//, '');\n\n  if (!!internalPayload.collapse_key) {\n    fcmEvent.collapse_key = internalPayload.collapse_key;\n  }\n\n  fcmEvent.event = EVENT_MESSAGE_DELIVERED.toString();\n\n  if (!!internalPayload.fcmOptions?.analytics_label) {\n    fcmEvent.analytics_label = internalPayload.fcmOptions?.analytics_label;\n  }\n\n  /* eslint-enable camelcase */\n  return fcmEvent;\n}\n\nfunction createAndEnqueueLogEvent(\n  messaging: MessagingService,\n  fcmEvent: FcmEvent,\n  productId: number\n): void {\n  const logEvent = {} as LogEvent;\n\n  /* eslint-disable camelcase */\n  logEvent.event_time_ms = Math.floor(Date.now()).toString();\n  logEvent.source_extension_json_proto3 = JSON.stringify({\n    messaging_client_event: fcmEvent\n  });\n\n  if (!!productId) {\n    logEvent.compliance_data = buildComplianceData(productId);\n  }\n  // eslint-disable-next-line camelcase\n\n  messaging.logEvents.push(logEvent);\n}\n\nfunction buildComplianceData(productId: number): ComplianceData {\n  const complianceData: ComplianceData = {\n    privacy_context: {\n      prequest: {\n        origin_associated_product_id: productId\n      }\n    }\n  };\n\n  return complianceData;\n}\n\nexport function _createLogRequest(logEventQueue: LogEvent[]): LogRequest {\n  const logRequest = {} as LogRequest;\n\n  /* eslint-disable camelcase */\n  logRequest.log_source = FCM_LOG_SOURCE.toString();\n  logRequest.log_event = logEventQueue;\n  /* eslint-enable camelcase */\n\n  return logRequest;\n}\n\nexport function _mergeStrings(s1: string, s2: string): string {\n  const resultArray = [];\n  for (let i = 0; i < s1.length; i++) {\n    resultArray.push(s1.charAt(i));\n    if (i < s2.length) {\n      resultArray.push(s2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseError } from '@firebase/util';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration Object');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: ReadonlyArray<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId',\n    'messagingSenderId'\n  ];\n\n  const { options } = app;\n  for (const keyName of configKeys) {\n    if (!options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: options.projectId!,\n    apiKey: options.apiKey!,\n    appId: options.appId!,\n    senderId: options.messagingSenderId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { MessagePayload, NextFn, Observer } from './interfaces/public-types';\n\nimport { FirebaseAnalyticsInternalName } from '@firebase/analytics-interop-types';\nimport { FirebaseInternalDependencies } from './interfaces/internal-dependencies';\nimport { LogEvent } from './interfaces/logging-types';\nimport { Provider } from '@firebase/component';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { extractAppConfig } from './helpers/extract-app-config';\n\nexport class MessagingService implements _FirebaseService {\n  readonly app!: FirebaseApp;\n  readonly firebaseDependencies!: FirebaseInternalDependencies;\n\n  swRegistration?: ServiceWorkerRegistration;\n  vapidKey?: string;\n  // logging is only done with end user consent. Default to false.\n  deliveryMetricsExportedToBigQueryEnabled: boolean = false;\n\n  onBackgroundMessageHandler:\n    | NextFn<MessagePayload>\n    | Observer<MessagePayload>\n    | null = null;\n\n  onMessageHandler: NextFn<MessagePayload> | Observer<MessagePayload> | null =\n    null;\n\n  logEvents: LogEvent[] = [];\n  isLogServiceStarted: boolean = false;\n\n  constructor(\n    app: FirebaseApp,\n    installations: _FirebaseInstallationsInternal,\n    analyticsProvider: Provider<FirebaseAnalyticsInternalName>\n  ) {\n    const appConfig = extractAppConfig(app);\n\n    this.firebaseDependencies = {\n      app,\n      appConfig,\n      installations,\n      analyticsProvider\n    };\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_REGISTRATION_TIMEOUT,\n  DEFAULT_SW_PATH,\n  DEFAULT_SW_SCOPE\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\n\nexport async function registerDefaultSw(\n  messaging: MessagingService\n): Promise<void> {\n  try {\n    messaging.swRegistration = await navigator.serviceWorker.register(\n      DEFAULT_SW_PATH,\n      {\n        scope: DEFAULT_SW_SCOPE\n      }\n    );\n\n    // The timing when browser updates sw when sw has an update is unreliable from experiment. It\n    // leads to version conflict when the SDK upgrades to a newer version in the main page, but sw\n    // is stuck with the old version. For example,\n    // https://github.com/firebase/firebase-js-sdk/issues/2590 The following line reliably updates\n    // sw if there was an update.\n    messaging.swRegistration.update().catch(() => {\n      /* it is non blocking and we don't care if it failed */\n    });\n    await waitForRegistrationActive(messaging.swRegistration);\n  } catch (e) {\n    throw ERROR_FACTORY.create(ErrorCode.FAILED_DEFAULT_REGISTRATION, {\n      browserErrorMessage: (e as Error)?.message\n    });\n  }\n}\n\n/**\n * Waits for registration to become active. MDN documentation claims that\n * a service worker registration should be ready to use after awaiting\n * navigator.serviceWorker.register() but that doesn't seem to be the case in\n * practice, causing the SDK to throw errors when calling\n * swRegistration.pushManager.subscribe() too soon after register(). The only\n * solution seems to be waiting for the service worker registration `state`\n * to become \"active\".\n */\nasync function waitForRegistrationActive(\n  registration: ServiceWorkerRegistration\n): Promise<void> {\n  return new Promise<void>((resolve, reject) => {\n    const rejectTimeout = setTimeout(\n      () =>\n        reject(\n          new Error(\n            `Service worker not registered after ${DEFAULT_REGISTRATION_TIMEOUT} ms`\n          )\n        ),\n      DEFAULT_REGISTRATION_TIMEOUT\n    );\n    const incomingSw = registration.installing || registration.waiting;\n    if (registration.active) {\n      clearTimeout(rejectTimeout);\n      resolve();\n    } else if (incomingSw) {\n      incomingSw.onstatechange = ev => {\n        if ((ev.target as ServiceWorker)?.state === 'activated') {\n          incomingSw.onstatechange = null;\n          clearTimeout(rejectTimeout);\n          resolve();\n        }\n      };\n    } else {\n      clearTimeout(rejectTimeout);\n      reject(new Error('No incoming service worker found.'));\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { getTokenInternal } from '../internals/token-manager';\nimport { updateSwReg } from '../helpers/updateSwReg';\nimport { updateVapidKey } from '../helpers/updateVapidKey';\nimport { GetTokenOptions } from '../interfaces/public-types';\n\nexport async function getToken(\n  messaging: MessagingService,\n  options?: GetTokenOptions\n): Promise<string> {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  if (Notification.permission === 'default') {\n    await Notification.requestPermission();\n  }\n\n  if (Notification.permission !== 'granted') {\n    throw ERROR_FACTORY.create(ErrorCode.PERMISSION_BLOCKED);\n  }\n\n  await updateVapidKey(messaging, options?.vapidKey);\n  await updateSwReg(messaging, options?.serviceWorkerRegistration);\n\n  return getTokenInternal(messaging);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY } from '../util/constants';\nimport { MessagingService } from '../messaging-service';\n\nexport async function updateVapidKey(\n  messaging: MessagingService,\n  vapidKey?: string | undefined\n): Promise<void> {\n  if (!!vapidKey) {\n    messaging.vapidKey = vapidKey;\n  } else if (!messaging.vapidKey) {\n    messaging.vapidKey = DEFAULT_VAPID_KEY;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { registerDefaultSw } from './registerDefaultSw';\n\nexport async function updateSwReg(\n  messaging: MessagingService,\n  swRegistration?: ServiceWorkerRegistration | undefined\n): Promise<void> {\n  if (!swRegistration && !messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n\n  if (!swRegistration && !!messaging.swRegistration) {\n    return;\n  }\n\n  if (!(swRegistration instanceof ServiceWorkerRegistration)) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_SW_REGISTRATION);\n  }\n\n  messaging.swRegistration = swRegistration;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\nimport {\n  ConsoleMessageData,\n  MessageType\n} from '../interfaces/internal-message-payload';\n\nimport { MessagingService } from '../messaging-service';\n\nexport async function logToScion(\n  messaging: MessagingService,\n  messageType: MessageType,\n  data: ConsoleMessageData\n): Promise<void> {\n  const eventType = getEventType(messageType);\n  const analytics =\n    await messaging.firebaseDependencies.analyticsProvider.get();\n  analytics.logEvent(eventType, {\n    /* eslint-disable camelcase */\n    message_id: data[CONSOLE_CAMPAIGN_ID],\n    message_name: data[CONSOLE_CAMPAIGN_NAME],\n    message_time: data[CONSOLE_CAMPAIGN_TIME],\n    message_device_time: Math.floor(Date.now() / 1000)\n    /* eslint-enable camelcase */\n  });\n}\n\nfunction getEventType(messageType: MessageType): string {\n  switch (messageType) {\n    case MessageType.NOTIFICATION_CLICKED:\n      return 'notification_open';\n    case MessageType.PUSH_RECEIVED:\n      return 'notification_foreground';\n    default:\n      throw new Error();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  MessagePayloadInternal,\n  MessageType\n} from '../interfaces/internal-message-payload';\n\nimport { CONSOLE_CAMPAIGN_ANALYTICS_ENABLED } from '../util/constants';\nimport { MessagingService } from '../messaging-service';\nimport { externalizePayload } from '../helpers/externalizePayload';\nimport { isConsoleMessage } from '../helpers/is-console-message';\nimport { logToScion } from '../helpers/logToScion';\n\nexport async function messageEventListener(\n  messaging: MessagingService,\n  event: MessageEvent\n): Promise<void> {\n  const internalPayload = event.data as MessagePayloadInternal;\n\n  if (!internalPayload.isFirebaseMessaging) {\n    return;\n  }\n\n  if (\n    messaging.onMessageHandler &&\n    internalPayload.messageType === MessageType.PUSH_RECEIVED\n  ) {\n    if (typeof messaging.onMessageHandler === 'function') {\n      messaging.onMessageHandler(externalizePayload(internalPayload));\n    } else {\n      messaging.onMessageHandler.next(externalizePayload(internalPayload));\n    }\n  }\n\n  // Log to Scion if applicable\n  const dataPayload = internalPayload.data;\n  if (\n    isConsoleMessage(dataPayload) &&\n    dataPayload[CONSOLE_CAMPAIGN_ANALYTICS_ENABLED] === '1'\n  ) {\n    await logToScion(messaging, internalPayload.messageType!, dataPayload);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSOLE_CAMPAIGN_ID } from '../util/constants';\nimport { ConsoleMessageData } from '../interfaces/internal-message-payload';\n\nexport function isConsoleMessage(data: unknown): data is ConsoleMessageData {\n  // This message has a campaign ID, meaning it was sent using the Firebase Console.\n  return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport {\n  onNotificationClick,\n  onPush,\n  onSubChange\n} from '../listeners/sw-listeners';\n\nimport { GetTokenOptions } from '../interfaces/public-types';\nimport { MessagingInternal } from '@firebase/messaging-interop-types';\nimport { MessagingService } from '../messaging-service';\nimport { ServiceWorkerGlobalScope } from '../util/sw-types';\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { getToken } from '../api/getToken';\nimport { messageEventListener } from '../listeners/window-listener';\n\nimport { name, version } from '../../package.json';\n\nconst WindowMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  navigator.serviceWorker.addEventListener('message', e =>\n    messageEventListener(messaging as MessagingService, e)\n  );\n\n  return messaging;\n};\n\nconst WindowMessagingInternalFactory: InstanceFactory<'messaging-internal'> = (\n  container: ComponentContainer\n) => {\n  const messaging = container\n    .getProvider('messaging')\n    .getImmediate() as MessagingService;\n\n  const messagingInternal: MessagingInternal = {\n    getToken: (options?: GetTokenOptions) => getToken(messaging, options)\n  };\n\n  return messagingInternal;\n};\n\ndeclare const self: ServiceWorkerGlobalScope;\nconst SwMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  self.addEventListener('push', e => {\n    e.waitUntil(onPush(e, messaging as MessagingService));\n  });\n  self.addEventListener('pushsubscriptionchange', e => {\n    e.waitUntil(onSubChange(e, messaging as MessagingService));\n  });\n  self.addEventListener('notificationclick', e => {\n    e.waitUntil(onNotificationClick(e));\n  });\n\n  return messaging;\n};\n\nexport function registerMessagingInWindow(): void {\n  _registerComponent(\n    new Component('messaging', WindowMessagingFactory, ComponentType.PUBLIC)\n  );\n\n  _registerComponent(\n    new Component(\n      'messaging-internal',\n      WindowMessagingInternalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\n/**\n * The messaging instance registered in sw is named differently than that of in client. This is\n * because both `registerMessagingInWindow` and `registerMessagingInSw` would be called in\n * `messaging-compat` and component with the same name can only be registered once.\n */\nexport function registerMessagingInSw(): void {\n  _registerComponent(\n    new Component('messaging-sw', SwMessagingFactory, ComponentType.PUBLIC)\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './util/errors';\nimport { FirebaseApp, _getProvider, getApp } from '@firebase/app';\nimport {\n  GetTokenOptions,\n  MessagePayload,\n  Messaging\n} from './interfaces/public-types';\nimport {\n  NextFn,\n  Observer,\n  Unsubscribe,\n  getModularInstance\n} from '@firebase/util';\nimport { isSwSupported, isWindowSupported } from './api/isSupported';\n\nimport { MessagingService } from './messaging-service';\nimport { deleteToken as _deleteToken } from './api/deleteToken';\nimport { getToken as _getToken } from './api/getToken';\nimport { onBackgroundMessage as _onBackgroundMessage } from './api/onBackgroundMessage';\nimport { onMessage as _onMessage } from './api/onMessage';\nimport { _setDeliveryMetricsExportedToBigQueryEnabled } from './api/setDeliveryMetricsExportedToBigQueryEnabled';\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInWindow(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isWindowSupported().then(\n    isSupported => {\n      // If `isWindowSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isWindowSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging').getImmediate();\n}\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInSw(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isSwSupported().then(\n    isSupported => {\n      // If `isSwSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isSwSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging-sw').getImmediate();\n}\n\n/**\n * Subscribes the {@link Messaging} instance to push notifications. Returns a Firebase Cloud\n * Messaging registration token that can be used to send push messages to that {@link Messaging}\n * instance.\n *\n * If notification permission isn't already granted, this method asks the user for permission. The\n * returned promise rejects if the user does not allow the app to show notifications.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param options - Provides an optional vapid key and an optional service worker registration.\n *\n * @returns The promise resolves with an FCM registration token.\n *\n * @public\n */\nexport async function getToken(\n  messaging: Messaging,\n  options?: GetTokenOptions\n): Promise<string> {\n  messaging = getModularInstance(messaging);\n  return _getToken(messaging as MessagingService, options);\n}\n\n/**\n * Deletes the registration token associated with this {@link Messaging} instance and unsubscribes\n * the {@link Messaging} instance from the push subscription.\n *\n * @param messaging - The {@link Messaging} instance.\n *\n * @returns The promise resolves when the token has been successfully deleted.\n *\n * @public\n */\nexport function deleteToken(messaging: Messaging): Promise<boolean> {\n  messaging = getModularInstance(messaging);\n  return _deleteToken(messaging as MessagingService);\n}\n\n/**\n * When a push message is received and the user is currently on a page for your origin, the\n * message is passed to the page and an `onMessage()` event is dispatched with the payload of\n * the push message.\n *\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined,\n *     is called when a message is received and the user is currently viewing your page.\n * @returns To stop listening for messages execute this returned function.\n *\n * @public\n */\nexport function onMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Called when a message is received while the app is in the background. An app is considered to be\n * in the background if no active window is displayed.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined, is called when a\n * message is received and the app is currently in the background.\n *\n * @returns To stop listening for messages execute this returned function\n *\n * @public\n */\nexport function onBackgroundMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onBackgroundMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Enables or disables Firebase Cloud Messaging message delivery metrics export to BigQuery. By\n * default, message delivery metrics are not exported to BigQuery. Use this method to enable or\n * disable the export at runtime.\n *\n * @param messaging - The `FirebaseMessaging` instance.\n * @param enable - Whether Firebase Cloud Messaging should export message delivery metrics to\n * BigQuery.\n *\n * @public\n */\nexport function experimentalSetDeliveryMetricsExportedToBigQueryEnabled(\n  messaging: Messaging,\n  enable: boolean\n): void {\n  messaging = getModularInstance(messaging);\n  return _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { deleteTokenInternal } from '../internals/token-manager';\nimport { registerDefaultSw } from '../helpers/registerDefaultSw';\n\nexport async function deleteToken(\n  messaging: MessagingService\n): Promise<boolean> {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  if (!messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n\n  return deleteTokenInternal(messaging);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport {\n  MessagePayload,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function onMessage(\n  messaging: MessagingService,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  messaging.onMessageHandler = nextOrObserver;\n\n  return () => {\n    messaging.onMessageHandler = null;\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, FCM_MSG } from '../util/constants';\nimport {\n  MessagePayloadInternal,\n  MessageType,\n  NotificationPayloadInternal\n} from '../interfaces/internal-message-payload';\nimport {\n  NotificationEvent,\n  PushEvent,\n  PushSubscriptionChangeEvent,\n  ServiceWorkerGlobalScope,\n  WindowClient\n} from '../util/sw-types';\nimport {\n  deleteTokenInternal,\n  getTokenInternal\n} from '../internals/token-manager';\n\nimport { MessagingService } from '../messaging-service';\nimport { dbGet } from '../internals/idb-manager';\nimport { externalizePayload } from '../helpers/externalizePayload';\nimport { isConsoleMessage } from '../helpers/is-console-message';\nimport { sleep } from '../helpers/sleep';\nimport { stageLog } from '../helpers/logToFirelog';\n\n// maxActions is an experimental property and not part of the official\n// TypeScript interface\n// https://developer.mozilla.org/en-US/docs/Web/API/Notification/maxActions\ninterface NotificationExperimental extends Notification {\n  maxActions?: number;\n}\n\n// Let TS know that this is a service worker\ndeclare const self: ServiceWorkerGlobalScope;\n\nexport async function onSubChange(\n  event: PushSubscriptionChangeEvent,\n  messaging: MessagingService\n): Promise<void> {\n  const { newSubscription } = event;\n  if (!newSubscription) {\n    // Subscription revoked, delete token\n    await deleteTokenInternal(messaging);\n    return;\n  }\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  await deleteTokenInternal(messaging);\n\n  messaging.vapidKey =\n    tokenDetails?.subscriptionOptions?.vapidKey ?? DEFAULT_VAPID_KEY;\n  await getTokenInternal(messaging);\n}\n\nexport async function onPush(\n  event: PushEvent,\n  messaging: MessagingService\n): Promise<void> {\n  const internalPayload = getMessagePayloadInternal(event);\n  if (!internalPayload) {\n    // Failed to get parsed MessagePayload from the PushEvent. Skip handling the push.\n    return;\n  }\n\n  // log to Firelog with user consent\n  if (messaging.deliveryMetricsExportedToBigQueryEnabled) {\n    await stageLog(messaging, internalPayload);\n  }\n\n  // foreground handling: eventually passed to onMessage hook\n  const clientList = await getClientList();\n  if (hasVisibleClients(clientList)) {\n    return sendMessagePayloadInternalToWindows(clientList, internalPayload);\n  }\n\n  // background handling: display if possible and pass to onBackgroundMessage hook\n  if (!!internalPayload.notification) {\n    await showNotification(wrapInternalPayload(internalPayload));\n  }\n\n  if (!messaging) {\n    return;\n  }\n\n  if (!!messaging.onBackgroundMessageHandler) {\n    const payload = externalizePayload(internalPayload);\n\n    if (typeof messaging.onBackgroundMessageHandler === 'function') {\n      await messaging.onBackgroundMessageHandler(payload);\n    } else {\n      messaging.onBackgroundMessageHandler.next(payload);\n    }\n  }\n}\n\nexport async function onNotificationClick(\n  event: NotificationEvent\n): Promise<void> {\n  const internalPayload: MessagePayloadInternal =\n    event.notification?.data?.[FCM_MSG];\n\n  if (!internalPayload) {\n    return;\n  } else if (event.action) {\n    // User clicked on an action button. This will allow developers to act on action button clicks\n    // by using a custom onNotificationClick listener that they define.\n    return;\n  }\n\n  // Prevent other listeners from receiving the event\n  event.stopImmediatePropagation();\n  event.notification.close();\n\n  // Note clicking on a notification with no link set will focus the Chrome's current tab.\n  const link = getLink(internalPayload);\n  if (!link) {\n    return;\n  }\n\n  // FM should only open/focus links from app's origin.\n  const url = new URL(link, self.location.href);\n  const originUrl = new URL(self.location.origin);\n\n  if (url.host !== originUrl.host) {\n    return;\n  }\n\n  let client = await getWindowClient(url);\n\n  if (!client) {\n    client = await self.clients.openWindow(link);\n\n    // Wait three seconds for the client to initialize and set up the message handler so that it\n    // can receive the message.\n    await sleep(3000);\n  } else {\n    client = await client.focus();\n  }\n\n  if (!client) {\n    // Window Client will not be returned if it's for a third party origin.\n    return;\n  }\n\n  internalPayload.messageType = MessageType.NOTIFICATION_CLICKED;\n  internalPayload.isFirebaseMessaging = true;\n  return client.postMessage(internalPayload);\n}\n\nfunction wrapInternalPayload(\n  internalPayload: MessagePayloadInternal\n): NotificationPayloadInternal {\n  const wrappedInternalPayload: NotificationPayloadInternal = {\n    ...(internalPayload.notification as unknown as NotificationPayloadInternal)\n  };\n\n  // Put the message payload under FCM_MSG name so we can identify the notification as being an FCM\n  // notification vs a notification from somewhere else (i.e. normal web push or developer generated\n  // notification).\n  wrappedInternalPayload.data = {\n    [FCM_MSG]: internalPayload\n  };\n\n  return wrappedInternalPayload;\n}\n\nfunction getMessagePayloadInternal({\n  data\n}: PushEvent): MessagePayloadInternal | null {\n  if (!data) {\n    return null;\n  }\n\n  try {\n    return data.json();\n  } catch (err) {\n    // Not JSON so not an FCM message.\n    return null;\n  }\n}\n\n/**\n * @param url The URL to look for when focusing a client.\n * @return Returns an existing window client or a newly opened WindowClient.\n */\nasync function getWindowClient(url: URL): Promise<WindowClient | null> {\n  const clientList = await getClientList();\n\n  for (const client of clientList) {\n    const clientUrl = new URL(client.url, self.location.href);\n\n    if (url.host === clientUrl.host) {\n      return client;\n    }\n  }\n\n  return null;\n}\n\n/**\n * @returns If there is currently a visible WindowClient, this method will resolve to true,\n * otherwise false.\n */\nfunction hasVisibleClients(clientList: WindowClient[]): boolean {\n  return clientList.some(\n    client =>\n      client.visibilityState === 'visible' &&\n      // Ignore chrome-extension clients as that matches the background pages of extensions, which\n      // are always considered visible for some reason.\n      !client.url.startsWith('chrome-extension://')\n  );\n}\n\nfunction sendMessagePayloadInternalToWindows(\n  clientList: WindowClient[],\n  internalPayload: MessagePayloadInternal\n): void {\n  internalPayload.isFirebaseMessaging = true;\n  internalPayload.messageType = MessageType.PUSH_RECEIVED;\n\n  for (const client of clientList) {\n    client.postMessage(internalPayload);\n  }\n}\n\nfunction getClientList(): Promise<WindowClient[]> {\n  return self.clients.matchAll({\n    type: 'window',\n    includeUncontrolled: true\n    // TS doesn't know that \"type: 'window'\" means it'll return WindowClient[]\n  }) as Promise<WindowClient[]>;\n}\n\nfunction showNotification(\n  notificationPayloadInternal: NotificationPayloadInternal\n): Promise<void> {\n  // Note: Firefox does not support the maxActions property.\n  // https://developer.mozilla.org/en-US/docs/Web/API/notification/maxActions\n  const { actions } = notificationPayloadInternal;\n  const { maxActions } = Notification as unknown as NotificationExperimental;\n  if (actions && maxActions && actions.length > maxActions) {\n    console.warn(\n      `This browser only supports ${maxActions} actions. The remaining actions will not be displayed.`\n    );\n  }\n\n  return self.registration.showNotification(\n    /* title= */ notificationPayloadInternal.title ?? '',\n    notificationPayloadInternal\n  );\n}\n\nfunction getLink(payload: MessagePayloadInternal): string | null {\n  // eslint-disable-next-line camelcase\n  const link = payload.fcmOptions?.link ?? payload.notification?.click_action;\n  if (link) {\n    return link;\n  }\n\n  if (isConsoleMessage(payload.data)) {\n    // Notification created in the Firebase Console. Redirect to origin.\n    return self.location.origin;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport {\n  MessagePayload,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function onBackgroundMessage(\n  messaging: MessagingService,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  if (self.document !== undefined) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_SW);\n  }\n\n  messaging.onBackgroundMessageHandler = nextOrObserver;\n\n  return () => {\n    messaging.onBackgroundMessageHandler = null;\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp as AppCompat,\n  _FirebaseService\n} from '@firebase/app-compat';\nimport {\n  Messaging,\n  MessagePayload,\n  deleteToken,\n  getToken,\n  onMessage\n} from '@firebase/messaging';\nimport {\n  areCookiesEnabled,\n  isIndexedDBAvailable,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '@firebase/util';\n\nimport { onBackgroundMessage } from '@firebase/messaging/sw';\n\nexport interface MessagingCompat {\n  getToken(options?: {\n    vapidKey?: string;\n    serviceWorkerRegistration?: ServiceWorkerRegistration;\n  }): Promise<string>;\n\n  deleteToken(): Promise<boolean>;\n\n  onMessage(\n    nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n  ): Unsubscribe;\n\n  onBackgroundMessage(\n    nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n  ): Unsubscribe;\n}\n\nexport function isSupported(): boolean {\n  if (self && 'ServiceWorkerGlobalScope' in self) {\n    // Running in ServiceWorker context\n    return isSwSupported();\n  } else {\n    // Assume we are in the window context.\n    return isWindowSupported();\n  }\n}\n\n/**\n * Checks to see if the required APIs exist.\n * Unlike the modular version, it does not check if IndexedDB.open() is allowed\n * in order to keep isSupported() synchronous and maintain v8 compatibility.\n */\nfunction isWindowSupported(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    isIndexedDBAvailable() &&\n    areCookiesEnabled() &&\n    'serviceWorker' in navigator &&\n    'PushManager' in window &&\n    'Notification' in window &&\n    'fetch' in window &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\n/**\n * Checks to see if the required APIs exist within SW Context.\n */\nfunction isSwSupported(): boolean {\n  return (\n    isIndexedDBAvailable() &&\n    'PushManager' in self &&\n    'Notification' in self &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\nexport class MessagingCompatImpl implements MessagingCompat, _FirebaseService {\n  constructor(readonly app: AppCompat, readonly _delegate: Messaging) {\n    this.app = app;\n    this._delegate = _delegate;\n  }\n\n  async getToken(options?: {\n    vapidKey?: string;\n    serviceWorkerRegistration?: ServiceWorkerRegistration;\n  }): Promise<string> {\n    return getToken(this._delegate, options);\n  }\n\n  async deleteToken(): Promise<boolean> {\n    return deleteToken(this._delegate);\n  }\n\n  onMessage(\n    nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n  ): Unsubscribe {\n    return onMessage(this._delegate, nextOrObserver);\n  }\n\n  onBackgroundMessage(\n    nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n  ): Unsubscribe {\n    return onBackgroundMessage(this._delegate, nextOrObserver);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport { MessagingCompatImpl, isSupported } from './messaging-compat';\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'messaging-compat': MessagingCompatImpl;\n  }\n}\n\nconst messagingCompatFactory: InstanceFactory<'messaging-compat'> = (\n  container: ComponentContainer\n) => {\n  if (self && 'ServiceWorkerGlobalScope' in self) {\n    // in sw\n    return new MessagingCompatImpl(\n      container.getProvider('app-compat').getImmediate(),\n      container.getProvider('messaging-sw').getImmediate()\n    );\n  } else {\n    // in window\n    return new MessagingCompatImpl(\n      container.getProvider('app-compat').getImmediate(),\n      container.getProvider('messaging').getImmediate()\n    );\n  }\n};\n\nconst NAMESPACE_EXPORTS = {\n  isSupported\n};\n\nexport function registerMessagingCompat(): void {\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component(\n      'messaging-compat',\n      messagingCompatFactory,\n      ComponentType.PUBLIC\n    ).setServiceProps(NAMESPACE_EXPORTS)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name, version } from '../package.json';\n\nimport firebase from '@firebase/app-compat';\nimport { registerMessagingCompat } from './registerMessagingCompat';\nimport { MessagingCompat } from './messaging-compat';\n\nregisterMessagingCompat();\nfirebase.registerVersion(name, version);\n\n/**\n * Define extension behavior of `registerMessaging`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    messaging: {\n      (app?: FirebaseApp): MessagingCompat;\n      isSupported(): boolean;\n    };\n  }\n  interface FirebaseApp {\n    messaging(): MessagingCompat;\n  }\n}\n"], "names": ["isIndexedDBAvailable", "indexedDB", "e", "FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "let", "idbProxyTraps", "get", "target", "prop", "receiver", "IDBTransaction", "objectStoreNames", "undefined", "objectStore", "wrap", "set", "has", "wrapFunction", "func", "IDBDatabase", "transaction", "IDBCursor", "advance", "continue", "continuePrimaryKey", "includes", "args", "apply", "unwrap", "storeNames", "tx", "call", "sort", "transformCachableValue", "done", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "complete", "error", "DOMException", "addEventListener", "IDBObjectStore", "IDBIndex", "Proxy", "request", "newValue", "IDBRequest", "promise", "success", "result", "then", "catch", "openDB", "version", "blocked", "upgrade", "blocking", "terminated", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "deleteDB", "deleteDatabase", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "useIndex", "isWrite", "method", "async", "storeName", "store", "index", "shift", "all", "oldTraps", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "INTERNAL_AUTH_VERSION", "INSTALLATIONS_API_URL", "TOKEN_EXPIRATION_BUFFER", "MessageType", "ERROR_FACTORY", "missing-app-config-values", "not-registered", "installation-not-found", "request-failed", "app-offline", "delete-pending-registration", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "Number", "creationTime", "Date", "now", "getErrorFromResponse", "requestName", "errorData", "await", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Content-Type", "Accept", "x-goog-api-key", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "retryIfServerError", "fn", "sleep", "ms", "setTimeout", "VALID_FID_PATTERN", "INVALID_FID", "generateFid", "fidByteArray", "Uint8Array", "fid", "self", "crypto", "msCrypto", "getRandomValues", "array", "btoa", "fromCharCode", "substr", "test", "<PERSON><PERSON><PERSON>", "appName", "appId", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "channel", "broadcastChannel", "BroadcastChannel", "onmessage", "postMessage", "size", "close", "callbacks", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "createObjectStore", "oldValue", "put", "remove", "delete", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "clearTimedOutRequest", "registrationStatus", "entryWithPromise", "inProgressEntry", "navigator", "onLine", "registrationTime", "registeredInstallationEntry", "heartbeatServiceProvider", "endpoint", "body", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "authVersion", "sdkVersion", "JSON", "stringify", "fetch", "ok", "responseValue", "authToken", "registrationPromiseWithError", "entry", "updateInstallationRequest", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "updateAuthTokenRequest", "inProgressAuthToken", "requestTime", "updatedInstallationEntry", "getToken", "installationsImpl", "getMissingValueError", "valueName", "INSTALLATIONS_NAME", "publicFactory", "app", "container", "get<PERSON><PERSON><PERSON>", "options", "keyName", "_get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "getId", "console", "_registerComponent", "registerVersion", "DEFAULT_SW_PATH", "DEFAULT_SW_SCOPE", "DEFAULT_VAPID_KEY", "ENDPOINT", "CONSOLE_CAMPAIGN_ID", "CONSOLE_CAMPAIGN_NAME", "CONSOLE_CAMPAIGN_TIME", "CONSOLE_CAMPAIGN_ANALYTICS_ENABLED", "DEFAULT_REGISTRATION_TIMEOUT", "arrayToBase64", "uint8Array", "OLD_DB_NAME", "OLD_DB_VERSION", "OLD_OBJECT_STORE_NAME", "migrateOldDatabase", "senderId", "databases", "map", "tokenDetails", "upgradeTransaction", "oldDetails", "contains", "clear", "auth", "p256dh", "fcmToken", "createTime", "subscriptionOptions", "swScope", "vapid<PERSON>ey", "length", "upgradeDb", "dbGet", "firebaseDependencies", "oldTokenDetails", "dbSet", "only-available-in-window", "only-available-in-sw", "permission-default", "permission-blocked", "unsupported-browser", "indexed-db-unsupported", "failed-service-worker-registration", "token-subscribe-failed", "token-subscribe-no-token", "token-unsubscribe-failed", "token-update-failed", "token-update-no-token", "use-sw-after-get-token", "invalid-sw-registration", "invalid-bg-handler", "invalid-vapid-key", "use-vapid-key-after-get-token", "requestDeleteToken", "unsubscribeOptions", "responseData", "getEndpoint", "errorInfo", "err", "toString", "x-goog-firebase-installations-auth", "getBody", "web", "applicationPubKey", "TOKEN_EXPIRATION_MS", "getTokenInternal", "messaging", "dbOptions", "isVapidKeyEqual", "isEndpointEqual", "isAuthEqual", "isP256dhEqual", "pushSubscription", "swRegistration", "subscription", "pushManager", "getSubscription", "subscribe", "userVisibleOnly", "applicationServerKey", "base64String", "base64", "repeat", "rawData", "atob", "outputArray", "i", "charCodeAt", "scope", "updatedToken", "updateOptions", "updatedTokenDetails", "warn", "getNewToken", "deleteTokenInternal", "unsubscribe", "subscribeOptions", "externalizePayload", "internalPayload", "payload", "messagePayloadInternal", "analyticsLabel", "from", "<PERSON><PERSON>ey", "collapse_key", "messageId", "fcmMessageId", "notification", "title", "image", "icon", "fcmOptions", "click_action", "link", "analytics_label", "_mergeStrings", "s1", "s2", "resultArray", "push", "char<PERSON>t", "join", "MessagingService", "analyticsProvider", "deliveryMetricsExportedToBigQueryEnabled", "onBackgroundMessageHandler", "onMessageHandler", "logEvents", "isLogServiceStarted", "messagingSenderId", "registerDefaultSw", "serviceWorker", "register", "registration", "rejectTimeout", "incomingSw", "installing", "waiting", "active", "clearTimeout", "onstatechange", "ev", "state", "browserErrorMessage", "Notification", "permission", "requestPermission", "serviceWorkerRegistration", "ServiceWorkerRegistration", "logToScion", "messageType", "eventType", "NOTIFICATION_CLICKED", "PUSH_RECEIVED", "logEvent", "message_id", "message_name", "message_time", "message_device_time", "Math", "floor", "messageEventListener", "dataPayload", "isFirebaseMessaging", "next", "WindowMessagingFactory", "WindowMessagingInternalFactory", "deleteToken", "onMessage", "nextOrObserver", "_onMessage", "FCM_MSG", "SDK_PLATFORM_WEB", "EVENT_MESSAGE_DELIVERED", "stageLog", "fcmEvent", "project_number", "instance_id", "message_type", "DISPLAY_NOTIFICATION", "DATA_MESSAGE", "sdk_platform", "package_name", "origin", "productId", "event_time_ms", "source_extension_json_proto3", "messaging_client_event", "compliance_data", "privacy_context", "prequest", "origin_associated_product_id", "onPush", "clientList", "getClientList", "client", "visibilityState", "url", "startsWith", "sendMessagePayloadInternalToWindows", "notificationPayloadInternal", "actions", "maxActions", "showNotification", "wrappedInternalPayload", "onNotificationClick", "action", "stopImmediatePropagation", "location", "URL", "href", "originUrl", "host", "clientUrl", "focus", "clients", "openWindow", "matchAll", "includeUncontrolled", "SwMessagingFactory", "waitUntil", "newSubscription", "onBackgroundMessage", "_onBackgroundMessage", "document", "MessagingCompatImpl", "_getToken", "messagingCompatFactory", "NAMESPACE_EXPORTS", "isSupported", "hasOwnProperty", "PushSubscription", "window", "cookieEnabled", "firebase", "INTERNAL", "registerComponent"], "mappings": "yaAwFa,mBCsGGA,IACd,IACE,MAA4B,UAArB,OAAOC,SAGf,CAFC,MAAOC,GACP,MAAO,CAAA,CACR,CACH,OC3HaC,UAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,CAAO,EALJG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,EAAcW,SAAS,EAI/CV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,EAAaF,UAAUG,MAAM,CAE9D,CACF,OAEYD,EAIXX,YACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,CACf,CAEJH,OACEX,KACGe,GAEH,IAcuCA,EAdjCb,EAAca,EAAK,IAAoB,GACvCC,EAAcZ,KAAKQ,QAAR,IAAmBZ,EAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,GAUuBF,EAVcb,EAAVe,EAW7BC,QAAQC,EAAS,CAACC,EAAGC,KACnC,IAAMC,EAAQP,EAAKM,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,CAAK,MAAQD,KAC7C,CAAC,GAdoE,QAE7DG,EAAiBpB,KAAKS,iBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,EAAcmB,EAAUQ,EAAatB,CAAU,CAGlE,CACF,CASD,IAAMiB,EAAU,gBClHV,SAAUM,EACdb,GAEA,OAAIA,GAAYA,EAA+Bc,UACrCd,EAA+Bc,UAEhCd,CAEX,OCDae,EAiBX5B,YACWM,EACAuB,EACAC,GAFAzB,KAAIC,KAAJA,EACAD,KAAewB,gBAAfA,EACAxB,KAAIyB,KAAJA,EAnBXzB,KAAiB0B,kBAAG,CAAA,EAIpB1B,KAAY2B,aAAe,GAE3B3B,KAAA4B,kBAA2C,OAE3C5B,KAAiB6B,kBAAwC,IAYrD,CAEJC,qBAAqBC,GAEnB,OADA/B,KAAK4B,kBAAoBG,EAClB/B,IACR,CAEDgC,qBAAqBN,GAEnB,OADA1B,KAAK0B,kBAAoBA,EAClB1B,IACR,CAEDiC,gBAAgBC,GAEd,OADAlC,KAAK2B,aAAeO,EACblC,IACR,CAEDmC,2BAA2BC,GAEzB,OADApC,KAAK6B,kBAAoBO,EAClBpC,IACR,CACF,CCtED,IAAMqC,EAAgB,CAACC,EAAQC,IAAiBA,EAAaC,KAAK,GAAOF,aAAkBG,CAAC,EAExFC,EACAC,EAqBJ,IAAMC,EAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,EAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlCK,IAAIC,EAAgB,CAChBC,IAAIC,EAAQC,EAAMC,GACd,GAAIF,aAAkBG,eAAgB,CAElC,GAAa,SAATF,EACA,OAAOR,EAAmBM,IAAIC,CAAM,EAExC,GAAa,qBAATC,EACA,OAAOD,EAAOI,kBAAoBV,EAAyBK,IAAIC,CAAM,EAGzE,GAAa,UAATC,EACA,OAAOC,EAASE,iBAAiB,GAC3BC,KAAAA,EACAH,EAASI,YAAYJ,EAASE,iBAAiB,EAAE,CAE9D,CAED,OAAOG,EAAKP,EAAOC,EAAK,CAC3B,EACDO,IAAIR,EAAQC,EAAMpC,GAEd,OADAmC,EAAOC,GAAQpC,EACR,CAAA,CACV,EACD4C,IAAIT,EAAQC,GACR,OAAID,aAAkBG,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQD,CAClB,CACL,EAIA,SAASU,EAAaC,GAIlB,OAAIA,IAASC,YAAY7D,UAAU8D,aAC7B,qBAAsBV,eAAepD,WA7GnCuC,EAAAA,GACoB,CACpBwB,UAAU/D,UAAUgE,QACpBD,UAAU/D,UAAUiE,SACpBF,UAAU/D,UAAUkE,qBAqHEC,SAASP,CAAI,EAChC,YAAaQ,GAIhB,OADAR,EAAKS,MAAMC,EAAO1E,IAAI,EAAGwE,CAAI,EACtBZ,EAAKhB,EAAiBQ,IAAIpD,IAAI,CAAC,CAClD,EAEW,YAAawE,GAGhB,OAAOZ,EAAKI,EAAKS,MAAMC,EAAO1E,IAAI,EAAGwE,CAAI,CAAC,CAClD,EAvBe,SAAUG,KAAeH,GAC5B,IAAMI,EAAKZ,EAAKa,KAAKH,EAAO1E,IAAI,EAAG2E,EAAY,GAAGH,CAAI,EAEtD,OADAzB,EAAyBc,IAAIe,EAAID,EAAWG,KAAOH,EAAWG,KAAM,EAAG,CAACH,EAAW,EAC5Ef,EAAKgB,CAAE,CAC1B,CAoBA,CACA,SAASG,EAAuB7D,GAC5B,IA5FoC0D,EAI9BI,EAwFN,MAAqB,YAAjB,OAAO9D,EACA6C,EAAa7C,CAAK,GAGzBA,aAAiBsC,iBAhGeoB,EAiGD1D,EA/F/B4B,EAAmBgB,IAAIc,CAAE,IAEvBI,EAAO,IAAIC,QAAQ,CAACC,EAASC,KAC/B,IAAMC,EAAW,KACbR,EAAGS,oBAAoB,WAAYC,CAAQ,EAC3CV,EAAGS,oBAAoB,QAASE,CAAK,EACrCX,EAAGS,oBAAoB,QAASE,CAAK,CACjD,EACcD,EAAW,KACbJ,IACAE,GACZ,EACcG,EAAQ,KACVJ,EAAOP,EAAGW,OAAS,IAAIC,aAAa,aAAc,YAAY,CAAC,EAC/DJ,GACZ,EACQR,EAAGa,iBAAiB,WAAYH,CAAQ,EACxCV,EAAGa,iBAAiB,QAASF,CAAK,EAClCX,EAAGa,iBAAiB,QAASF,CAAK,CAC1C,CAAK,EAEDzC,EAAmBe,IAAIe,EAAII,CAAI,IA2E3B3C,EAAcnB,EAzJVwB,EAAAA,GACiB,CACjBuB,YACAyB,eACAC,SACAxB,UACAX,eAmJuC,EACpC,IAAIoC,MAAM1E,EAAOiC,CAAa,EAElCjC,EACX,CACA,SAAS0C,EAAK1C,GAGV,IA1IsB2E,EAgJhBC,EANN,OAAI5E,aAAiB6E,YA1ICF,EA2IM3E,GA1ItB8E,EAAU,IAAIf,QAAQ,CAACC,EAASC,KAClC,IAAMC,EAAW,KACbS,EAAQR,oBAAoB,UAAWY,CAAO,EAC9CJ,EAAQR,oBAAoB,QAASE,CAAK,CACtD,EACcU,EAAU,KACZf,EAAQtB,EAAKiC,EAAQK,MAAM,CAAC,EAC5Bd,GACZ,EACcG,EAAQ,KACVJ,EAAOU,EAAQN,KAAK,EACpBH,GACZ,EACQS,EAAQJ,iBAAiB,UAAWQ,CAAO,EAC3CJ,EAAQJ,iBAAiB,QAASF,CAAK,CAC/C,CAAK,GAEIY,KAAK,IAGFjF,aAAiBiD,WACjBvB,EAAiBiB,IAAI3C,EAAO2E,CAAO,CAG/C,CAAK,EACIO,MAAM,MAAS,EAGpBnD,EAAsBY,IAAImC,EAASH,CAAO,EACnCG,GAgHHhD,EAAec,IAAI5C,CAAK,EACjB8B,EAAeI,IAAIlC,CAAK,IAC7B4E,EAAWf,EAAuB7D,CAAK,KAG5BA,IACb8B,EAAea,IAAI3C,EAAO4E,CAAQ,EAClC7C,EAAsBY,IAAIiC,EAAU5E,CAAK,GAEtC4E,EACX,CACA,IAAMpB,EAAS,GAAWzB,EAAsBG,IAAIlC,CAAK,EL5KzD,SAASmF,EAAOpG,EAAMqG,EAAS,CAAEC,QAAAA,EAASC,QAAAA,EAASC,SAAAA,EAAUC,WAAAA,CAAY,EAAG,IACxE,IAAMb,EAAUtG,UAAUoH,KAAK1G,EAAMqG,CAAO,EAC5C,IAAMM,EAAchD,EAAKiC,CAAO,EAoBhC,OAnBIW,GACAX,EAAQJ,iBAAiB,gBAAiB,IACtCe,EAAQ5C,EAAKiC,EAAQK,MAAM,EAAGW,EAAMC,WAAYD,EAAME,WAAYnD,EAAKiC,EAAQ3B,WAAW,EAAG2C,CAAK,CAC9G,CAAS,EAEDN,GACAV,EAAQJ,iBAAiB,UAAW,GAAWc,EAE/CM,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,EAE9CD,EACKT,KAAK,IACFO,GACAM,EAAGvB,iBAAiB,QAAS,IAAMiB,EAAY,CAAA,EAC/CD,GACAO,EAAGvB,iBAAiB,gBAAiB,GAAWgB,EAASI,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,CAE/G,CAAK,EACIT,MAAM,MAAS,EACbQ,CACX,CAMA,SAASK,EAAShH,EAAM,CAAEsG,QAAAA,CAAO,EAAK,IAClC,IAAMV,EAAUtG,UAAU2H,eAAejH,CAAI,EAM7C,OALIsG,GACAV,EAAQJ,iBAAiB,UAAW,GAAWc,EAE/CM,EAAMC,WAAYD,CAAK,CAAC,EAErBjD,EAAKiC,CAAO,EAAEM,KAAK,MAAe,CAC7C,CAEA,IAAMgB,EAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,EAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAIC,IAC1B,SAASC,EAAUlE,EAAQC,GACvB,GAAMD,aAAkBY,aAClB,EAAAX,KAAQD,IACM,UAAhB,OAAOC,EAFX,CAKA,GAAI+D,EAAcjE,IAAIE,CAAI,EACtB,OAAO+D,EAAcjE,IAAIE,CAAI,EACjC,IAAMkE,EAAiBlE,EAAKxC,QAAQ,aAAc,EAAE,EAC9C2G,EAAWnE,IAASkE,EACpBE,EAAUN,EAAa7C,SAASiD,CAAc,EACpD,IAMMG,EANN,OAEEH,KAAmBC,EAAW9B,SAAWD,gBAAgBtF,YACrDsH,GAAWP,EAAY5C,SAASiD,CAAc,IAG9CG,EAASC,eAAgBC,KAAcrD,GAEzC,IAAMI,EAAK5E,KAAKkE,YAAY2D,EAAWH,EAAU,YAAc,UAAU,EACzExE,IAAIG,EAASuB,EAAGkD,MAQhB,OAPIL,IACApE,EAASA,EAAO0E,MAAMvD,EAAKwD,MAAO,CAAA,IAM/B,MAAO/C,QAAQgD,IAAI,CACtB5E,EAAOmE,GAAgB,GAAGhD,CAAI,EAC9BkD,GAAW9C,EAAGI,KACjB,GAAG,EACZ,EACIqC,EAAcxD,IAAIP,EAAMqE,CAAM,EACvBA,GAvBP,KAAA,CANC,CA8BL,CKgCIxE,EL/BwB,CACxB,GADS,EK+BgBA,EL7BzBC,IAAK,CAACC,EAAQC,EAAMC,IAAagE,EAAUlE,EAAQC,CAAI,GAAK4E,EAAS9E,IAAIC,EAAQC,EAAMC,CAAQ,EAC/FO,IAAK,CAACT,EAAQC,IAAS,CAAC,CAACiE,EAAUlE,EAAQC,CAAI,GAAK4E,EAASpE,IAAIT,EAAQC,CAAI,CAChF,6CMzEM,IAAM6E,EAAqB,IAErBC,EAAkB,KAAK9B,EACvB+B,EAAwB,SAExBC,GACX,kDAEWC,GAA0B,KAEhC,ICgDKC,EC7BAA,ED6BAA,EAAAA,EE1BL,IAAMC,EAAgB,IAAInI,EHtBV,gBACK,gBGD2C,CACrEoI,4BACE,kDACFC,iBAA4B,2CAC5BC,yBAAoC,mCACpCC,iBACE,6FACFC,cAAyB,kDACzBC,8BACE,2EAgBmB,EAYjB,SAAUC,GAAczD,GAC5B,OACEA,aAAiB9F,GACjB8F,EAAM3F,KAAK2E,SAAQ,iBAEvB,CCxCgB,SAAA0E,GAAyB,CAAEC,UAAAA,IACzC,OAAUZ,gBAAkCY,iBAC9C,CAEM,SAAUC,GACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,UAgEKC,OAhEwCJ,EAASG,UAgExBzI,QAAQ,IAAK,KAAK,CAAC,EA/DjD2I,aAAcC,KAAKC,IAAK,EAE5B,CAEO/B,eAAegC,GACpBC,EACAT,GAEA,IACMU,GAD8BC,MAAMX,EAASY,QACpBzE,MAC/B,OAAOkD,EAAclI,OAAiC,iBAAA,CACpDsJ,YAAAA,EACAI,WAAYH,EAAUlK,KACtBsK,cAAeJ,EAAUjK,QACzBsK,aAAcL,EAAUM,MACzB,CAAA,CACH,CAEgB,SAAAC,GAAW,CAAEC,OAAAA,IAC3B,OAAO,IAAIC,QAAQ,CACjBC,eAAgB,mBAChBC,OAAQ,mBACRC,iBAAkBJ,CACnB,CAAA,CACH,CAEgB,SAAAK,GACdC,EACA,CAAEC,aAAAA,IAEF,IAAMC,EAAUT,GAAWO,CAAS,EAEpC,OADAE,EAAQC,OAAO,iBAmCeF,EAnCyBA,EAoC7CxC,EAAH,IAA4BwC,EApCiC,EAC7DC,CACT,CAeOlD,eAAeoD,GACpBC,GAEA,IAAM/E,EAAS6D,MAAMkB,IAErB,OAAqB,KAAjB/E,EAAOkE,QAAiBlE,EAAOkE,OAAS,IAEnCa,EAAE,EAGJ/E,CACT,CCnFM,SAAUgF,GAAMC,GACpB,OAAO,IAAIlG,QAAcC,IACvBkG,WAAWlG,EAASiG,CAAE,CACxB,CAAC,CACH,CCHO,IAAME,GAAoB,oBACpBC,EAAc,GAMX,SAAAC,KACd,IAGE,IAAMC,EAAe,IAAIC,WAAW,EAAE,EAQhCC,IANJC,KAAKC,QAAWD,KAAyCE,UACpDC,gBAAgBN,CAAY,EAGnCA,EAAa,GAAK,IAAcA,EAAa,GAAK,ICnBhBO,GACxBC,KAAK7K,OAAO8K,aAAa,GAAGF,CAAK,CAAC,EACnCjL,QAAQ,MAAO,GAAG,EAAEA,QAAQ,MAAO,GAAG,GDmB5B0K,CAW+B,EAInCU,OAAO,EAAG,EAAE,GAb3B,OAAOb,GAAkBc,KAAKT,CAAG,EAAIA,EAAMJ,CAI5C,CAHC,MAEA,OAAOA,CACR,CACH,CEzBM,SAAUc,EAAOxB,GACrB,OAAUA,EAAUyB,QAAb,IAAwBzB,EAAU0B,KAC3C,CCDA,IAAMC,GAA2D,IAAIjF,IAMrD,SAAAkF,GAAW5B,EAAsBc,GAC/C,IAAMzK,EAAMmL,EAAOxB,CAAS,EAwDF3J,GAtD1BwL,GAAuBxL,EAAKyK,CAAG,EACZzK,GAsDbyL,GASR,KACM,CAACC,GAAoB,qBAAsBhB,QAC7CgB,EAAmB,IAAIC,iBAAiB,uBAAuB,GAC9CC,UAAYrN,IAC3BiN,GAAuBjN,EAAEmB,KAAKM,IAAKzB,EAAEmB,KAAK+K,GAAG,CAC/C,GAEKiB,MAfHD,GACFA,EAAQI,YAAY,CAAE7L,IAAAA,EAAKyK,IAAAA,CAAK,CAAA,EAkBF,IAA5Ba,GAAmBQ,MAAcJ,IACnCA,EAAiBK,MAAK,EACtBL,EAAmB,KA3EvB,CAyCA,SAASF,GAAuBxL,EAAayK,GAC3C,IAAMuB,EAAYV,GAAmBnJ,IAAInC,CAAG,EAC5C,GAAKgM,EAIL,IAAK,IAAM7K,KAAY6K,EACrB7K,EAASsJ,CAAG,CAEhB,CAUAxI,IAAIyJ,EAA4C,KCrEhD,IAAMO,GAAgB,kCAChBC,GAAmB,EACnBC,EAAoB,+BAStBC,GAA2D,KAC/D,SAASC,IAgBP,OAfKD,GAAAA,IACShH,EAAO6G,GAAeC,GAAkB,CAClD3G,QAAS,CAACQ,EAAIF,KAOL,IADCA,GAEJE,EAAGuG,kBAAkBH,CAAiB,CAE3C,CACF,CAAA,CAGL,CAeOxF,eAAe/D,EACpB+G,EACA1J,GAEA,IAAMD,EAAMmL,EAAOxB,CAAS,EAEtBhG,GADKmF,MAAMuD,KACHpJ,YAAYkJ,EAAmB,WAAW,EAClDzJ,EAAciB,EAAGjB,YAAYyJ,CAAiB,EAC9CI,EAAQ,MAAU7J,EAAYP,IAAInC,CAAG,EAQ3C,OAPA8I,MAAMpG,EAAY8J,IAAIvM,EAAOD,CAAG,EAChC8I,MAAMnF,EAAGI,KAEJwI,GAAYA,EAAS9B,MAAQxK,EAAMwK,KACtCc,GAAW5B,EAAW1J,EAAMwK,GAAG,EAG1BxK,CACT,CAGO0G,eAAe8F,GAAO9C,GAC3B,IAAM3J,EAAMmL,EAAOxB,CAAS,EAEtBhG,GADKmF,MAAMuD,KACHpJ,YAAYkJ,EAAmB,WAAW,EACxDrD,MAAMnF,EAAGjB,YAAYyJ,CAAiB,EAAEO,OAAO1M,CAAG,EAClD8I,MAAMnF,EAAGI,IACX,CAQO4C,eAAegG,EACpBhD,EACAiD,GAEA,IAAM5M,EAAMmL,EAAOxB,CAAS,EAEtBhG,GADKmF,MAAMuD,KACHpJ,YAAYkJ,EAAmB,WAAW,EAClDtF,EAAQlD,EAAGjB,YAAYyJ,CAAiB,EACxCI,EAAQ,MAAyC1F,EAAM1E,IAC3DnC,CAAG,EAEC6E,EAAW+H,EAASL,CAAQ,EAalC,OAXiB9J,KAAAA,IAAboC,EACFiE,MAAMjC,EAAM6F,OAAO1M,CAAG,EAEtB8I,MAAMjC,EAAM2F,IAAI3H,EAAU7E,CAAG,EAE/B8I,MAAMnF,EAAGI,KAELc,CAAAA,GAAc0H,GAAYA,EAAS9B,MAAQ5F,EAAS4F,KACtDc,GAAW5B,EAAW9E,EAAS4F,GAAG,EAG7B5F,CACT,CClFO8B,eAAekG,EACpBC,GAEA7K,IAAI8K,EAEJ,IAAMC,EAAoBlE,MAAM6D,EAAOG,EAAcnD,UAAWsD,IAC9D,IAAMD,EAgCDE,GAhCqDD,GA2Bf,CAC3CxC,IAAKH,GAAa,EAClB6C,mBAA6C,EAGd,EA/BzBC,GAyCV,CACEN,EACAE,KAEA,IAaQK,EAKAN,EAlBR,OAAwC,IAApCC,EAAkBG,mBACfG,UAAUC,QAYTF,EAA+C,CACnD5C,IAAKuC,EAAkBvC,IACvB0C,mBAA6C,EAC7CK,iBAAkB/E,KAAKC,IAAK,GAExBqE,GAkBVpG,MACEmG,EACAE,KAEA,IACE,IAAMS,EAA8B3E,MCxGjCnC,MACL,CAAEgD,UAAAA,EAAW+D,yBAAAA,CAAwB,EACrC,CAAEjD,IAAAA,CAAG,KAEL,IAAMkD,EAAW3F,GAAyB2B,CAAS,EAEnD,IAAME,EAAUT,GAAWO,CAAS,EAa9BiE,IAPFC,EAHqBH,EAAyBI,aAAa,CAC7DC,SAAU,CAAA,CACX,CAAA,KAEOC,EAAmBlF,MAAM+E,EAAiBI,wBAE9CpE,EAAQC,OAAO,oBAAqBkE,CAAgB,EAI3C,CACXvD,IAAAA,EACAyD,YAAa9G,EACbiE,MAAO1B,EAAU0B,MACjB8C,WAAYhH,IAGd,IAAMvC,EAAuB,CAC3B8B,OAAQ,OACRmD,QAAAA,EACA+D,KAAMQ,KAAKC,UAAUT,CAAI,GAI3B,IADMzF,EAAWW,MAAMiB,GAAmB,IAAMuE,MAAMX,EAAU/I,CAAO,CAAC,GAC3D2J,GAQX,MANiE,CAC/D9D,KAFI+D,EAA4C1F,MAAMX,EAASY,QAE5C0B,KAAOA,EAC1B0C,mBAA2C,EAC3CvD,aAAc4E,EAAc5E,aAC5B6E,UAAWvG,GAAiCsG,EAAcC,SAAS,GAIrE,MAAM3F,MAAMH,GAAqB,sBAAuBR,CAAQ,CAEpE,GD4DM2E,EACAE,CAAiB,EAEnB,OAAOpK,EAAIkK,EAAcnD,UAAW8D,CAA2B,CAchE,CAbC,MAAOlP,GAYP,MAXIwJ,GAAcxJ,CAAC,GAAiC,MAA5BA,EAAEM,WAAWmK,WAGnCF,MAAM2D,GAAOK,EAAcnD,SAAS,EAGpCb,MAAMlG,EAAIkK,EAAcnD,UAAW,CACjCc,IAAKuC,EAAkBvC,IACvB0C,mBAA6C,CAC9C,CAAA,EAEG5O,CACP,CACH,GAzCMuO,EACAO,CAAe,EAEV,CAAEL,kBAAmBK,EAAiBN,oBAAAA,KAnBrC2B,EAA+B1K,QAAQE,OAC3CsD,EAAclI,OAA6B,aAAA,CAAA,EAEtC,CACL0N,kBAAAA,EACAD,oBAAqB2B,IAgBW,IAApC1B,EAAkBG,mBAEX,CACLH,kBAAAA,EACAD,qBAmCNpG,MACEmG,IAMA7K,IAAI0M,EAA2B7F,MAAM8F,GACnC9B,EAAcnD,SAAS,EAEzB,KAA+B,IAAxBgF,EAAMxB,oBAEXrE,MAAMmB,GAAM,GAAG,EAEf0E,EAAQ7F,MAAM8F,GAA0B9B,EAAcnD,SAAS,EAGjE,IAEUqD,EAAmBD,EAF7B,OAA4B,IAAxB4B,EAAMxB,mBAaHwB,GAXC,CAAE3B,kBAAAA,EAAmBD,oBAAAA,CAAmB,EAC5CjE,MAAM+D,EAAqBC,CAAa,EAEtCC,GAIKC,EAKb,GAlEoDF,CAAa,GAGtD,CAAEE,kBAAAA,CAAiB,CAE9B,GA7EMF,EACAE,CAAiB,EAGnB,OADAD,EAAsBK,EAAiBL,oBAChCK,EAAiBJ,iBAC1B,CAAC,EAED,OAAIA,EAAkBvC,MAAQJ,EAErB,CAAE2C,kBAAmBlE,MAAMiE,GAG7B,CACLC,kBAAAA,EACAD,oBAAAA,EAEJ,CAoIA,SAAS6B,GACPjF,GAEA,OAAOgD,EAAOhD,EAAWsD,IACvB,GAAKA,EAGL,OAAOC,GAAqBD,CAAQ,EAFlC,MAAMzF,EAAclI,OAAM,yBAG9B,CAAC,CACH,CAEA,SAAS4N,GAAqByB,GAC5B,IAWA3B,EAXA,OAcoE,KAHpEA,EAXmC2B,GAcfxB,oBAClBH,EAAkBQ,iBAAmBtG,EAAqBuB,KAAKC,IAAG,EAd3D,CACL+B,IAAKkE,EAAMlE,IACX0C,mBAA6C,GAI1CwB,CACT,CEzLOhI,eAAekI,GACpB,CAAElF,UAAAA,EAAW+D,yBAAAA,CAAwB,EACrCV,GAEiB8B,CAwCjBnF,EACEc,GAzCeqE,CAA6BnF,EAAWqD,OAAzD,IAAMW,EA2CI3F,GAAyB2B,CAAS,MAAKc,wBAJnD,IACEd,EACEc,EAvCIZ,EAAUH,GAAmBC,EAAWqD,CAAiB,EAGzDa,EAAmBH,EAAyBI,aAAa,CAC7DC,SAAU,CAAA,CACX,CAAA,EAQKH,GAPFC,IACIG,EAAmBlF,MAAM+E,EAAiBI,wBAE9CpE,EAAQC,OAAO,oBAAqBkE,CAAgB,EAI3C,CACXe,aAAc,CACZZ,WAAYhH,EACZkE,MAAO1B,EAAU0B,KAClB,IAGH,IAAMzG,EAAuB,CAC3B8B,OAAQ,OACRmD,QAAAA,EACA+D,KAAMQ,KAAKC,UAAUT,CAAI,GAGrBzF,EAAWW,MAAMiB,GAAmB,IAAMuE,MAAMX,EAAU/I,CAAO,CAAC,EACxE,GAAIuD,EAASoG,GAIX,OADErG,GAF+CY,MAAMX,EAASY,MAEhB,EAGhD,MAAMD,MAAMH,GAAqB,sBAAuBR,CAAQ,CAEpE,CCnCOxB,eAAeqI,GACpBlC,EACAmC,EAAe,CAAA,GAEfhN,IAAIiN,EACJ,IAAMP,EAAQ7F,MAAM6D,EAAOG,EAAcnD,UAAWsD,IAClD,GAAI,CAACkC,GAAkBlC,CAAQ,EAC7B,MAAMzF,EAAclI,OAAM,kBAG5B,IAgIsBmP,EAhIhBW,EAAenC,EAASwB,UAC9B,GAAKQ,GAiI8C,KAF7BR,EA/HgBW,GAiI5B/G,gBAKcoG,IAC1B,IAAM/F,EAAMD,KAAKC,MACjB,OACEA,EAAM+F,EAAUjG,cAChBiG,EAAUjG,aAAeiG,EAAUnG,UAAYI,EAAMpB,EAEzD,GAVwBmH,CAAS,EA/HtB,CAAA,GAA8B,IAA1BW,EAAa/G,cAGtB,OADA6G,GA0BNvI,MACEmG,EACAmC,KAMAhN,IAAI0M,EAAQ7F,MAAMuG,GAAuBvC,EAAcnD,SAAS,EAChE,KAAoC,IAA7BgF,EAAMF,UAAUpG,eAErBS,MAAMmB,GAAM,GAAG,EAEf0E,EAAQ7F,MAAMuG,GAAuBvC,EAAcnD,SAAS,EAG9D,IAAM8E,EAAYE,EAAMF,UACxB,OAA2B,IAAvBA,EAAUpG,cAEL2G,GAAiBlC,EAAemC,CAAY,EAE5CR,CAEX,GAjD+C3B,EAAemC,CAAY,EAC7DhC,EAGP,GAAKK,UAAUC,OAMf,OAiIJN,EAnIgEA,EAqI1DqC,EAA2C,CAC/CjH,cAAwC,EACxCkH,YAAa9G,KAAKC,IAAK,GAvIf2E,EAyIH,CACL,GAAGJ,EACHwB,UAAWa,GA1ITJ,GAsENvI,MACEmG,EACAE,KAEA,IACE,IAAMyB,EAAY3F,MAAM+F,GACtB/B,EACAE,CAAiB,EAEbwC,EAAwD,CAC5D,GAAGxC,EACHyB,UAAAA,GAGF,OADA3F,MAAMlG,EAAIkK,EAAcnD,UAAW6F,CAAwB,EACpDf,CAiBR,CAhBC,MAAOlQ,GACP,IAQQiR,EAMR,KAbEzH,CAAAA,GAAcxJ,CAAC,GACc,MAA5BA,EAAEM,WAAWmK,YAAkD,MAA5BzK,EAAEM,WAAWmK,YAM3CwG,EAAwD,CAC5D,GAAGxC,EACHyB,UAAW,CAAEpG,cAAa,CAA6B,GAEzDS,MAAMlG,EAAIkK,EAAcnD,UAAW6F,CAAwB,GAN3D1G,MAAM2D,GAAOK,EAAcnD,SAAS,EAQhCpL,CACP,CACH,GAtG8CuO,EAAeO,CAAe,EAC/DA,EALL,MAAM7F,EAAclI,OAAM,cAM7B,CAdC,OAAO2N,CAeX,CAAC,EAKD,OAHkBiC,EACdpG,MAAMoG,EACLP,EAAMF,SAEb,CAyCA,SAASY,GACP1F,GAEA,OAAOgD,EAAOhD,EAAWsD,IACvB,IAIMmC,EAoF2BX,EAxFjC,GAAKU,GAAkBlC,CAAQ,EAK/B,OADMmC,EAAenC,EAASwB,UAsFuB,KAFpBA,EAnFDW,GAqFtB/G,eACVoG,EAAUc,YAAcrI,EAAqBuB,KAAKC,IAAG,EArF5C,CACL,GAAGuE,EACHwB,UAAW,CAAEpG,cAAa,CAA6B,GAIpD4E,EAXL,MAAMzF,EAAclI,OAAM,iBAY9B,CAAC,CACH,CAoCA,SAAS6P,GACPnC,GAEA,OACwBvK,KAAAA,IAAtBuK,GACgE,IAAhEA,EAAkBG,kBAEtB,CCnJOxG,eAAe8I,GACpB3C,EACAmC,EAAe,CAAA,GAEf,IAAMS,EAAoB5C,EAKpB2B,GAJN3F,MAaIiE,EAFIA,GAAwBjE,MAAM+D,EAXC6C,CAWiC,GAA3C,sBAI3B5G,CAAAA,MAAMiE,GAXUjE,MAAMkG,GAAiBU,EAAmBT,CAAY,GACxE,OAAOR,EAAUrG,KACnB,CCWA,SAASuH,GAAqBC,GAC5B,OAAOpI,EAAclI,OAA4C,4BAAA,CAC/DsQ,UAAAA,CACD,CAAA,CACH,CC3BA,IAAMC,GAAqB,gBAGrBC,GAAkD,IAGtD,IAAMC,EAAMC,EAAUC,YAAY,KAAK,EAAEnC,aAAY,EAWrD,MANqD,KACnDiC,EACApG,WDpB6BoG,IAC/B,GAAI,CAACA,GAAO,CAACA,EAAIG,QACf,MAAMP,GAAqB,mBAAmB,EAGhD,GAAI,CAACI,EAAI/Q,KACP,MAAM2Q,GAAqB,UAAU,EAIvC,IAMWQ,EAAX,IAAWA,IANsC,CAC/C,YACA,SACA,SAIA,GAAI,CAACJ,EAAIG,QAAQC,GACf,MAAMR,GAAqBQ,CAAO,EAItC,MAAO,CACL/E,QAAS2E,EAAI/Q,KACbiJ,UAAW8H,EAAIG,QAAQjI,UACvBoB,OAAQ0G,EAAIG,QAAQ7G,OACpBgC,MAAO0E,EAAIG,QAAQ7E,MAEvB,GCbqC0E,CAAG,EAMpCrC,yBAL+B0C,GAAAA,aAAaL,EAAK,WAAW,EAM5DM,QAAS,IAAMrM,QAAQC,QAAS,EAGpC,EAEMqM,GAA6D,IAGjE,IAAMP,EAAMC,EAAUC,YAAY,KAAK,EAAEnC,aAAY,EAErD,IAAMhB,EAAgBsD,GAAAA,aAAaL,EAAKF,EAAkB,EAAE/B,aAAY,EAMxE,MAJ8D,CAC5DyC,MAAO,KC5BJ5J,MAAqBmG,IAC1B,IAAM4C,EAAoB5C,EACpB,CAAEE,kBAAAA,EAAmBD,oBAAAA,CAAmB,EAAKjE,MAAM+D,EACvD6C,CAAiB,EAWnB,OARI3C,GAKFiC,GAAiBU,CAAiB,GAJdvK,MAAMqL,QAAQlM,KAAK,EAOlC0I,EAAkBvC,GAC3B,GDauBqC,CAAa,EAChC2C,SAAU,GAA4BA,GAAS3C,EAAemC,CAAY,EAG9E,EAGEwB,GAAkBA,mBAChB,IAAInQ,EAAUuP,GAAoBC,GAAoC,QAAA,CAAA,EAExEW,GAAkBA,mBAChB,IAAInQ,EAtC4B,yBAwC9BgQ,GAED,SAAA,CAAA,EExCLI,GAAAA,gBAAgB1R,EAAMqG,CAAO,EAE7BqL,GAAAA,gBAAgB1R,EAAMqG,EAAS,SAAkB,EjBjB1C,IAAMsL,GAAkB,4BAClBC,GAAmB,uCAEnBC,GACX,0FAEWC,GAAW,6CAKXC,GAAsB,kBACtBC,GAAwB,iBACxBC,GAAwB,gBAExBC,GAAqC,eAMrCC,GAA+B,IkBrBtC,SAAUC,EAActG,GAC5B,IAAMuG,EAAa,IAAI7G,WAAWM,CAAK,EAEvC,OADqBC,KAAK7K,OAAO8K,aAAa,GAAGqG,CAAU,CAAC,EACxCxR,QAAQ,KAAM,EAAE,EAAEA,QAAQ,MAAO,GAAG,EAAEA,QAAQ,MAAO,GAAG,CAC9E,EnBwDY0H,EAAAA,EAAAA,GAGX,IAFC,cAAA,gBACAA,EAAA,qBAAA,uBoBlBF,IAAM+J,GAAc,uBAKdC,GAAiB,EACjBC,GAAwB,yBAEvB7K,eAAe8K,GACpBC,GAEA,GAAI,cAAepT,WAUb,EAPcwK,MAChBxK,UAGAqT,aACwBC,IAAI7L,GAAMA,EAAG/G,IAAI,EAE9BsE,SAASgO,EAAW,EAE/B,OAAO,KAIXrP,IAAI4P,EAAoC,KAkFxC,OAhFW/I,MAAM1D,EAAOkM,GAAaC,GAAgB,CACnDhM,QAASoB,MAAOZ,EAAIF,EAAYC,EAAYgM,KAC1C,IAWM7R,EA4CE8R,EAvDJlM,EAAa,GAKZE,EAAGvD,iBAAiBwP,SAASR,EAAqB,IAMjDvR,EAAQ6I,MADRpG,EAAcoP,EAAmBpP,YAAY8O,EAAqB,GACxC1K,MAAM,aAAa,EAAE3E,IAAIuP,CAAQ,EACjE5I,MAAMpG,EAAYuP,QAEbhS,KAKc,IAAf4F,GACIkM,EAAa9R,GAEHiS,MAASH,EAAWI,QAAWJ,EAAWpE,WAI1DkE,EAAe,CACbzJ,MAAO2J,EAAWK,SAClBC,WAAYN,EAAWM,YAAc5J,KAAKC,IAAK,EAC/C4J,oBAAqB,CACnBJ,KAAMH,EAAWG,KACjBC,OAAQJ,EAAWI,OACnBxE,SAAUoE,EAAWpE,SACrB4E,QAASR,EAAWQ,QACpBC,SACiC,UAA/B,OAAOT,EAAWS,SACdT,EAAWS,SACXpB,EAAcW,EAAWS,QAAQ,CACxC,IAEqB,IAAf3M,GACHkM,EAAa9R,EAEnB4R,EAAe,CACbzJ,MAAO2J,EAAWK,SAClBC,WAAYN,EAAWM,WACvBC,oBAAqB,CACnBJ,KAAMd,EAAcW,EAAWG,IAAI,EACnCC,OAAQf,EAAcW,EAAWI,MAAM,EACvCxE,SAAUoE,EAAWpE,SACrB4E,QAASR,EAAWQ,QACpBC,SAAUpB,EAAcW,EAAWS,QAAQ,CAC5C,IAEqB,IAAf3M,IACHkM,EAAa9R,EAEnB4R,EAAe,CACbzJ,MAAO2J,EAAWK,SAClBC,WAAYN,EAAWM,WACvBC,oBAAqB,CACnBJ,KAAMd,EAAcW,EAAWG,IAAI,EACnCC,OAAQf,EAAcW,EAAWI,MAAM,EACvCxE,SAAUoE,EAAWpE,SACrB4E,QAASR,EAAWQ,QACpBC,SAAUpB,EAAcW,EAAWS,QAAQ,CAC5C,IAGN,CACF,CAAA,GACEzG,MAAK,EAGRjD,MAAM9C,EAASsL,EAAW,EAC1BxI,MAAM9C,EAAS,sBAAsB,EACrC8C,MAAM9C,EAAS,WAAW,GAM1B6L,IAEA,IAGQS,EAHR,GAAKT,GAAiBA,EAAaS,oBAInC,OADQA,EAAwBT,EAAH,oBAEQ,UAAnC,OAAOA,EAAaQ,YACM,EAA1BR,EAAaQ,YACiB,UAA9B,OAAOR,EAAazJ,OACQ,EAA5ByJ,EAAazJ,MAAMqK,QACiB,UAApC,OAAOH,EAAoBJ,MACO,EAAlCI,EAAoBJ,KAAKO,QACa,UAAtC,OAAOH,EAAoBH,QACS,EAApCG,EAAoBH,OAAOM,QACa,UAAxC,OAAOH,EAAoB3E,UACW,EAAtC2E,EAAoB3E,SAAS8E,QACU,UAAvC,OAAOH,EAAoBC,SACU,EAArCD,EAAoBC,QAAQE,QACY,UAAxC,OAAOH,EAAoBE,UACW,EAAtCF,EAAoBE,SAASC,MAEjC,GA1B2BZ,CAAY,EAAIA,EAAe,IAC1D,CCnJO,IAAM5F,GAAgB,8BACvBC,GAAmB,EACnBC,EAAoB,2BAStBC,GAAuD,KAC3D,SAASC,KAeP,OAdKD,GAAAA,IACShH,EAAO6G,GAAeC,GAAkB,CAClD3G,QAAS,CAACmN,EAAW7M,KAMZ,IADCA,GAEJ6M,EAAUpG,kBAAkBH,CAAiB,CAElD,CACF,CAAA,CAGL,CAGOxF,eAAegM,GACpBC,GAEA,IAAM5S,EAAMmL,GAAOyH,CAAoB,EAEjCf,EAAgB,MADX/I,MAAMuD,MAEdpJ,YAAYkJ,CAAiB,EAC7BzJ,YAAYyJ,CAAiB,EAC7BhK,IAAInC,CAAG,EAEV,OAAI6R,KAIIgB,EAAkB/J,MAAM2I,GAC5BmB,EAAqBjJ,UAAU+H,QAAQ,IAGvC5I,MAAMgK,GAAMF,EAAsBC,CAAe,EAC1CA,GAFT,KAAA,EAKJ,CAGOlM,eAAemM,GACpBF,EACAf,GAEA,IAAM7R,EAAMmL,GAAOyH,CAAoB,EAEjCjP,GADKmF,MAAMuD,MACHpJ,YAAYkJ,EAAmB,WAAW,EAGxD,OAFArD,MAAMnF,EAAGjB,YAAYyJ,CAAiB,EAAEK,IAAIqF,EAAc7R,CAAG,EAC7D8I,MAAMnF,EAAGI,KACF8N,CACT,CAsBA,SAAS1G,GAAO,CAAExB,UAAAA,IAChB,OAAOA,EAAU0B,KACnB,CCvBO,IAAM7D,EAAgB,IAAInI,EAC/B,YACA,YArD4C,CAC5CoI,4BACE,kDACFsL,2BACE,gDACFC,uBACE,wDACFC,qBACE,qEACFC,qBACE,mEACFC,sBACE,2EACFC,yBACE,mGACFC,qCACE,+EACFC,yBACE,qEACFC,2BACE,2DACFC,2BACE,yEAEFC,sBACE,oEACFC,wBACE,wDACFC,yBACE,4IAEFC,0BACE,uEACFC,qBACE,iEACFC,oBAA+B,yCAC/BC,gCACE,wIAiBO,ECwBJpN,eAAeqN,GACpBpB,EACAxK,GAEA,IAEM6L,EAAqB,CACzBvN,OAAQ,SACRmD,QAJcf,MAAMM,GAAWwJ,CAAoB,GAOrD,IACE,IAMQhU,EAFFsV,EAA4BpL,MAJjBA,MAAMwF,MAClB6F,GAAYvB,EAAqBjJ,SAAS,EAA7C,IAAkDvB,EAClD6L,CAAkB,GAE6BlL,OACjD,GAAImL,EAAa5P,MAEf,MADM1F,EAAUsV,EAAa5P,MAAM1F,QAC7B4I,EAAclI,OAA2C,2BAAA,CAC7D8U,UAAWxV,CACZ,CAAA,CAMJ,CAJC,MAAOyV,GACP,MAAM7M,EAAclI,OAA2C,2BAAA,CAC7D8U,UAAYC,GAAeC,SAAU,CACtC,CAAA,CACF,CACH,CAEA,SAASH,GAAY,CAAElM,UAAAA,IACrB,OAAU6I,gBAAqB7I,iBACjC,CAEAtB,eAAeyC,GAAW,CACxBO,UAAAA,EACAmD,cAAAA,IAEA,IAAM2B,EAAY3F,MAAMgE,EAAc2C,WAEtC,OAAO,IAAInG,QAAQ,CACjBC,eAAgB,mBAChBC,OAAQ,mBACRC,iBAAkBE,EAAUN,OAC5BkL,qCAAsC,OAAO9F,CAC9C,CAAA,CACH,CAEA,SAAS+F,GAAQ,CACfrC,OAAAA,EACAD,KAAAA,EACAvE,SAAAA,EACA6E,SAAAA,CAAQ,GAER,IAAM5E,EAAuB,CAC3B6G,IAAK,CACH9G,SAAAA,EACAuE,KAAAA,EACAC,OAAAA,CACD,GAOH,OAJIK,IAAa3B,KACfjD,EAAK6G,IAAIC,kBAAoBlC,GAGxB5E,CACT,CCxJA,IAAM+G,GAAsB,OAErBhO,eAAeiO,GACpBC,GAEA,IAwIAC,EAGMC,EACAC,EACAC,EACAC,EA9IAC,EAAmBrM,MAmH3BnC,MACEyO,EACA5C,KAEA,IAAM6C,EAAevM,MAAMsM,EAAeE,YAAYC,gBAAe,EACrE,OAAIF,GAIGD,EAAeE,YAAYE,UAAU,CAC1CC,gBAAiB,CAAA,EAGjBC,sBL/I0BC,IAC5B,IACMC,GAAUD,EADA,IAAIE,QAAQ,EAAKF,EAAalD,OAAS,GAAM,CAAC,GAE3D5S,QAAQ,MAAO,GAAG,EAClBA,QAAQ,KAAM,GAAG,EAEdiW,EAAUC,KAAKH,CAAM,EACrBI,EAAc,IAAIxL,WAAWsL,EAAQrD,MAAM,EAEjD,IAAKxQ,IAAIgU,EAAI,EAAGA,EAAIH,EAAQrD,OAAQ,EAAEwD,EACpCD,EAAYC,GAAKH,EAAQI,WAAWD,CAAC,EAEvC,OAAOD,CACT,GKkIwCxD,CAAQ,CAC7C,CAAA,CACH,GAjIIqC,EAAUO,eACVP,EAAUrC,QAAS,EAGfF,EAA2C,CAC/CE,SAAUqC,EAAUrC,SACpBD,QAASsC,EAAUO,eAAgBe,MACnCxI,SAAUwH,EAAiBxH,SAC3BuE,KAAMd,EAAc+D,EAAiBhK,OAAO,MAAM,CAAE,EACpDgH,OAAQf,EAAc+D,EAAiBhK,OAAO,QAAQ,CAAE,GAGpD0G,EAAe/I,MAAM6J,GAAMkC,EAAUjC,oBAAoB,EAC/D,GAAKf,EAAL,CAGO,GAuHPiD,EAtHgBjD,EAAaS,oBAyHvByC,EAzH6CzC,EAyHZE,WAAasC,EAAUtC,SACxDwC,EA1H6C1C,EA0HZ3E,WAAamH,EAAUnH,SACxDsH,EA3H6C3C,EA2HhBJ,OAAS4C,EAAU5C,KAChDgD,EA5H6C5C,EA4HdH,SAAW2C,EAAU3C,OAEnD4C,GAAmBC,GAAmBC,GAAeC,EAhHrD,OAAIzM,KAAKC,IAAG,GAAMmJ,EAAaQ,WAAasC,IAwCrDhO,MACEkO,EACAhD,KAEA,IACE,IAAMuE,EAAetN,MDrClBnC,MACLiM,EACAf,KAEA,IAAMhI,EAAUf,MAAMM,GAAWwJ,CAAoB,EAC/ChF,EAAO4G,GAAQ3C,EAAaS,mBAAoB,EAEhD+D,EAAgB,CACpB3P,OAAQ,QACRmD,QAAAA,EACA+D,KAAMQ,KAAKC,UAAUT,CAAI,GAG3B3L,IAAIiS,EACJ,IACE,IAAM/L,EAAWW,MAAMwF,MAClB6F,GAAYvB,EAAqBjJ,SAAS,EAA7C,IAAkDkI,EAAazJ,MAC/DiO,CAAa,EAEfnC,EAAepL,MAAMX,EAASY,MAK/B,CAJC,MAAOsL,GACP,MAAM7M,EAAclI,OAAsC,sBAAA,CACxD8U,UAAYC,GAAeC,SAAU,CACtC,CAAA,CACF,CAED,GAAIJ,EAAa5P,MAEf,MADM1F,EAAUsV,EAAa5P,MAAM1F,QAC7B4I,EAAclI,OAAsC,sBAAA,CACxD8U,UAAWxV,CACZ,CAAA,EAGH,GAAKsV,EAAa9L,MAIlB,OAAO8L,EAAa9L,MAHlB,MAAMZ,EAAclI,OAAM,wBAI9B,GCAMuV,EAAUjC,qBACVf,CAAY,EAGRyE,EAAoC,CACxC,GAAGzE,EACHzJ,MAAOgO,EACP/D,WAAY5J,KAAKC,IAAK,GAIxB,OADAI,MAAMgK,GAAM+B,EAAUjC,qBAAsB0D,CAAmB,EACxDF,CAGR,CAFC,MAAO7X,GACP,MAAMA,CACP,CACH,GA3DuBsW,EAAW,CAC5BzM,MAAOyJ,EAAazJ,MACpBiK,WAAY5J,KAAKC,IAAK,EACtB4J,oBAAAA,CACD,CAAA,EAGMT,EAAazJ,MApBpB,IACEU,MAAMkL,GACJa,EAAUjC,qBACVf,EAAazJ,KAAK,CAKrB,CAHC,MAAO7J,GAEPiS,QAAQ+F,KAAKhY,CAAC,CACf,CAaF,CA1BC,OAAOiY,GAAY3B,EAAUjC,qBAAsBN,CAAmB,CA2B1E,CAMO3L,eAAe8P,GACpB5B,GAEA,IHQMlR,EGRAkO,EAAe/I,MAAM6J,GAAMkC,EAAUjC,oBAAoB,EAUzDuC,GATFtD,IACF/I,MAAMkL,GACJa,EAAUjC,qBACVf,EAAazJ,KAAK,EHEhBpI,EAAMmL,GGAK0J,EAAUjC,oBHAY,EAGvC9J,MADMnF,GADKmF,MAAMuD,MACHpJ,YAAYkJ,EAAmB,WAAW,GAC/CzJ,YAAYyJ,CAAiB,EAAEO,OAAO1M,CAAG,EAClD8I,MAAMnF,EAAGI,MGCP+E,MAAM+L,EAAUO,eAAgBE,YAAYC,gBAAe,GAC7D,MAAIJ,CAAAA,GACKA,EAAiBuB,aAK5B,CAyBA/P,eAAe6P,GACb5D,EACAN,GAEA,IAIMT,EAA6B,CACjCzJ,MALYU,MDnGTnC,MACLiM,EACAN,KAEA,IAAMzI,EAAUf,MAAMM,GAAWwJ,CAAoB,EAC/ChF,EAAO4G,GAAQlC,CAAmB,EAElCqE,EAAmB,CACvBjQ,OAAQ,OACRmD,QAAAA,EACA+D,KAAMQ,KAAKC,UAAUT,CAAI,GAG3B3L,IAAIiS,EACJ,IACE,IAAM/L,EAAWW,MAAMwF,MACrB6F,GAAYvB,EAAqBjJ,SAAS,EAC1CgN,CAAgB,EAElBzC,EAAepL,MAAMX,EAASY,MAK/B,CAJC,MAAOsL,GACP,MAAM7M,EAAclI,OAAyC,yBAAA,CAC3D8U,UAAYC,GAAeC,SAAU,CACtC,CAAA,CACF,CAED,GAAIJ,EAAa5P,MAEf,MADM1F,EAAUsV,EAAa5P,MAAM1F,QAC7B4I,EAAclI,OAAyC,yBAAA,CAC3D8U,UAAWxV,CACZ,CAAA,EAGH,GAAKsV,EAAa9L,MAIlB,OAAO8L,EAAa9L,MAHlB,MAAMZ,EAAclI,OAAM,2BAI9B,GC8DIsT,EACAN,CAAmB,EAInBD,WAAY5J,KAAKC,IAAK,EACtB4J,oBAAAA,GAGF,OADAxJ,MAAMgK,GAAMF,EAAsBf,CAAY,EACvCA,EAAazJ,KACtB,CChIM,SAAUwO,GACdC,GAEA,IA0DAC,EACAC,EAqBMC,EAhFAF,EAA0B,CAC9BG,KAAMJ,EAAgBI,KAEtBC,YAAaL,EAAgBM,aAE7BC,UAAWP,EAAgBQ,cAO7B,OAIAP,EAR6BA,GAS7BC,EATsCF,GAWVS,eAI5BR,EAAQQ,aAAe,IAEjBC,EAAQR,EAAuBO,aAAcC,SAEjDT,EAAQQ,aAAcC,MAAQA,IAG1B3J,EAAOmJ,EAAuBO,aAAc1J,QAEhDkJ,EAAQQ,aAAc1J,KAAOA,IAGzB4J,EAAQT,EAAuBO,aAAcE,SAEjDV,EAAQQ,aAAcE,MAAQA,GAG1BC,EAAOV,EAAuBO,aAAcG,QAEhDX,EAAQQ,aAAcG,KAAOA,GAK/BX,EAtCqBA,GAuCrBC,EAvC8BF,GAyCFnX,OAI5BoX,EAAQpX,KAAOqX,EAAuBrX,MAItCoX,EAhDoBA,IAiDpBC,EAjD6BF,GAqDHa,YACvBX,EAAuBO,cAAcK,gBAKxCb,EAAQY,WAAa,IAEfE,EACJb,EAAuBW,YAAYE,MACnCb,EAAuBO,cAAcK,gBAGrCb,EAAQY,WAAYE,KAAOA,GAIvBZ,EAAiBD,EAAuBW,YAAYG,mBAExDf,EAAQY,WAAYV,eAAiBA,GAtEhCF,CACT,CCI0BgB,IAkNIC,GAjN5B,uBAiNwCC,GAhNxC,sBAiNMC,GAAc,GACpB,IAAKhW,IAAIgU,EAAI,EAAGA,EAAI8B,GAAGtF,OAAQwD,CAAC,GAC9BgC,GAAYC,KAAKH,GAAGI,OAAOlC,CAAC,CAAC,EACzBA,EAAI+B,GAAGvF,QACTwF,GAAYC,KAAKF,GAAGG,OAAOlC,CAAC,CAAC,ECvMnC,SAAStG,GAAqBC,GAC5B,OAAOpI,EAAclI,OAA4C,4BAAA,CAC/DsQ,UAAAA,CACD,CAAA,CACH,CDuMSqI,GAAYG,KAAK,EAAE,QExOfC,GAoBX3Z,YACEqR,EACAjD,EACAwL,GAhBFvZ,KAAwCwZ,yCAAY,CAAA,EAEpDxZ,KAA0ByZ,2BAGf,KAEXzZ,KAAgB0Z,iBACd,KAEF1Z,KAAS2Z,UAAe,GACxB3Z,KAAmB4Z,oBAAY,CAAA,EAO7B,IAAMhP,GD7BuBoG,IAC/B,GAAI,CAACA,GAAO,CAACA,EAAIG,QACf,MAAMP,GAAqB,0BAA0B,EAGvD,GAAI,CAACI,EAAI/Q,KACP,MAAM2Q,GAAqB,UAAU,EAIvC,IAQWQ,EADHD,EAAYH,EAAH,QACjB,IAAWI,IAR8C,CACvD,YACA,SACA,QACA,qBAKA,GAAI,CAACD,EAAQC,GACX,MAAMR,GAAqBQ,CAAO,EAItC,MAAO,CACL/E,QAAS2E,EAAI/Q,KACbiJ,UAAWiI,EAAQjI,UACnBoB,OAAQ6G,EAAQ7G,OAChBgC,MAAO6E,EAAQ7E,MACfqG,SAAUxB,EAAQ0I,kBAEtB,GCFuC7I,CAAG,EAEtChR,KAAK6T,qBAAuB,CAC1B7C,IAAAA,EACApG,UAAAA,EACAmD,cAAAA,EACAwL,kBAAAA,EAEH,CAEDjI,UACE,OAAOrM,QAAQC,SAChB,CACF,CCvCM0C,eAAekS,GACpBhE,GAEA,IACEA,EAAUO,eAAiBtM,MAAMwE,UAAUwL,cAAcC,SACvDpI,GACA,CACEwF,MAAOvF,EACR,CAAA,EAQHiE,EAAUO,eAAezI,OAAQ,EAACxH,MAAM,MAEvC,EAmBH6T,EAlBkCnE,EAAUO,eAA1CtM,MAoBK,IAAI9E,QAAc,CAACC,EAASC,KACjC,IAAM+U,EAAgB9O,WACpB,IACEjG,EACE,IAAIzF,6CACqC0S,OAAiC,CACzE,EAELA,EAA4B,EAExB+H,EAAaF,EAAaG,YAAcH,EAAaI,QACvDJ,EAAaK,QACfC,aAAaL,CAAa,EAC1BhV,KACSiV,EACTA,EAAWK,cAAgBC,IACmB,cAAvCA,EAAGpX,QAA0BqX,QAChCP,EAAWK,cAAgB,KAC3BD,aAAaL,CAAa,EAC1BhV,IAEJ,GAEAqV,aAAaL,CAAa,EAC1B/U,EAAO,IAAIzF,MAAM,mCAAmC,CAAC,EAEzD,CAAC,CAzCA,CAJC,MAAOF,GACP,MAAMiJ,EAAclI,OAA8C,qCAAA,CAChEoa,oBAAsBnb,GAAaK,OACpC,CAAA,CACF,CAYH+H,IACEqS,CAZF,CC1BOrS,eAAe8I,GACpBoF,EACA3E,GAEA,GAAI,CAAC5C,UACH,MAAM9F,EAAclI,OAAM,4BAO5B,GAJgC,YAA5Bqa,aAAaC,YACf9Q,MAAM6Q,aAAaE,oBAGW,YAA5BF,aAAaC,WACf,MAAMpS,EAAclI,OAAM,sBCjB5BuV,EDoBqBA,EAArB/L,KCjBA,GAFA0J,EDmBgCtC,GAASsC,UChBvCqC,EAAUrC,SAAWA,EACXqC,EAAUrC,WACpBqC,EAAUrC,SAAW3B,KAPlBlK,ICGLkO,EFmBkBA,EElBlBO,EFkB6BlF,GAAS4J,0BEZtC,GAJK1E,GAAmBP,EAAUO,gBAChCtM,MAAM+P,GAAkBhE,CAAS,EAG9BO,GAAoBP,CAAAA,EAAUO,eAAnC,CAIA,GAAI,EAAEA,aAA0B2E,2BAC9B,MAAMvS,EAAclI,OAAM,2BAG5BuV,EAAUO,eAAiBA,CAN1B,CFYD,OAFAtM,MAAAA,EAEO8L,GAAiBC,CAAS,CACnC,CGhBOlO,eAAeqT,GACpBnF,EACAoF,EACAva,GAEA,IAAMwa,GAacD,IACpB,OAAQA,GACN,KAAK1S,EAAY4S,qBACf,MAAO,oBACT,KAAK5S,EAAY6S,cACf,MAAO,0BACT,QACE,MAAM,IAAI3b,KACb,CACH,GAtBiCwb,CAAW,GAExCnR,MAAM+L,EAAUjC,qBAAqB0F,kBAAkBnW,IAAG,GAClDkY,SAASH,EAAW,CAE5BI,WAAY5a,EAAKqR,IACjBwJ,aAAc7a,EAAKsR,IACnBwJ,aAAc9a,EAAKuR,IACnBwJ,oBAAqBC,KAAKC,MAAMlS,KAAKC,IAAG,EAAK,GAAI,CAElD,CAAA,CACH,CCjBO/B,eAAeiU,GACpB/F,EACAjP,GAEA,IAkBMiV,EAlBAhE,EAAkBjR,EAAMlG,KAEzBmX,EAAgBiE,sBAKnBjG,EAAU4D,kBACV5B,EAAgBoD,cAAgB1S,EAAY6S,gBAEF,YAAtC,OAAOvF,EAAU4D,iBACnB5D,EAAU4D,iBAAiB7B,GAAmBC,CAAe,CAAC,EAE9DhC,EAAU4D,iBAAiBsC,KAAKnE,GAAmBC,CAAe,CAAC,GCvBhD,UAAhB,OAFwBnX,ED8BzBmb,EAAchE,EAAgBnX,QC5BCA,GAAQqR,MAAuBrR,GD+Bd,MAApDmb,EAAY3J,KAEZpI,MAAMkR,GAAWnF,EAAWgC,EAAgBoD,YAAcY,CAAW,CAEzE,2CElBMG,GAAuD,IAG3D,IAAMnG,EAAY,IAAIwD,GACpBrI,EAAUC,YAAY,KAAK,EAAEnC,aAAc,EAC3CkC,EAAUC,YAAY,wBAAwB,EAAEnC,aAAY,EAC5DkC,EAAUC,YAAY,oBAAoB,CAAC,EAO7C,OAJA3C,UAAUwL,cAActU,iBAAiB,UAAWjG,GAClDqc,GAAqB/F,EAA+BtW,CAAC,CAAC,EAGjDsW,CACT,EAEMoG,GAAwE,IAG5E,IAAMpG,EAAY7E,EACfC,YAAY,WAAW,EACvBnC,eAMH,MAJ6C,CAC3C2B,SAAU,GAA+BA,GAASoF,EAAW3E,CAAO,EAIxE,EC2DM,SAAUgL,GAAYrG,GAE1B,OCzGKlO,MACLkO,IAEA,GAAKvH,UAQL,OAJKuH,EAAUO,gBACbtM,MAAM+P,GAAkBhE,CAAS,EAG5B4B,GAAoB5B,CAAS,EAPlC,MAAMrN,EAAclI,OAAM,2BAQ9B,GD4FEuV,EAAYzU,EAAmByU,CAAS,CACS,CACnD,CAegB,SAAAsG,GACdtG,EACAuG,GAGOC,IEzHPxG,EFwHAA,EAAYzU,EAAmByU,CAAS,EEvHxCuG,EFwHiDA,EEtHjD,GAAK9N,UAML,OAFAuH,EAAU4D,iBAAmB2C,EAEtB,KACLvG,EAAU4D,iBAAmB,IAC/B,EAPE,MAAMjR,EAAclI,OAAM,2BFsH9B,CDzDEmR,GAAkBA,mBAChB,IAAInQ,EAAU,YAAa0a,GAA6C,QAAA,CAAA,EAG1EvK,GAAkBA,mBAChB,IAAInQ,EACF,qBACA2a,GAED,SAAA,CAAA,EAGHvK,mBAAgB1R,GAAMqG,EAAO,EAE7BqL,GAAAA,gBAAgB1R,GAAMqG,GAAS,SAAkB,EnCvF5C,IAAMwL,GACX,0FAEWC,GAAW,6CAGXwK,GAAU,UAEVvK,GAAsB,kBAiBtBwK,GAAmB,EACnBC,GAA0B,EkB7BjC,SAAUpK,EAActG,GAC5B,IAAMuG,EAAa,IAAI7G,WAAWM,CAAK,EAEvC,OADqBC,KAAK7K,OAAO8K,aAAa,GAAGqG,CAAU,CAAC,EACxCxR,QAAQ,KAAM,EAAE,EAAEA,QAAQ,MAAO,GAAG,EAAEA,QAAQ,MAAO,GAAG,CAC9E,ElB2BY0H,EAAAA,EAAAA,GAGX,IAFCA,EAAA,aAAA,GAAA,eACAA,EAAAA,EAAA,qBAAA,GAAA,wBD2BUA,EAAAA,EAAAA,GAGX,IAFC,cAAA,gBACAA,EAAA,qBAAA,uBoBlBF,IAAM+J,GAAc,uBAKdC,GAAiB,EACjBC,GAAwB,yBAEvB7K,eAAe8K,GACpBC,GAEA,GAAI,cAAepT,WAUb,EAPcwK,MAChBxK,UAGAqT,aACwBC,IAAI7L,GAAMA,EAAG/G,IAAI,EAE9BsE,SAASgO,EAAW,EAE/B,OAAO,KAIXrP,IAAI4P,EAAoC,KAkFxC,OAhFW/I,MAAM1D,EAAOkM,GAAaC,GAAgB,CACnDhM,QAASoB,MAAOZ,EAAIF,EAAYC,EAAYgM,KAC1C,IAWM7R,EA4CE8R,EAvDJlM,EAAa,GAKZE,EAAGvD,iBAAiBwP,SAASR,EAAqB,IAMjDvR,EAAQ6I,MADRpG,EAAcoP,EAAmBpP,YAAY8O,EAAqB,GACxC1K,MAAM,aAAa,EAAE3E,IAAIuP,CAAQ,EACjE5I,MAAMpG,EAAYuP,QAEbhS,KAKc,IAAf4F,GACIkM,EAAa9R,GAEHiS,MAASH,EAAWI,QAAWJ,EAAWpE,WAI1DkE,EAAe,CACbzJ,MAAO2J,EAAWK,SAClBC,WAAYN,EAAWM,YAAc5J,KAAKC,IAAK,EAC/C4J,oBAAqB,CACnBJ,KAAMH,EAAWG,KACjBC,OAAQJ,EAAWI,OACnBxE,SAAUoE,EAAWpE,SACrB4E,QAASR,EAAWQ,QACpBC,SACiC,UAA/B,OAAOT,EAAWS,SACdT,EAAWS,SACXpB,EAAcW,EAAWS,QAAQ,CACxC,IAEqB,IAAf3M,GACHkM,EAAa9R,EAEnB4R,EAAe,CACbzJ,MAAO2J,EAAWK,SAClBC,WAAYN,EAAWM,WACvBC,oBAAqB,CACnBJ,KAAMd,EAAcW,EAAWG,IAAI,EACnCC,OAAQf,EAAcW,EAAWI,MAAM,EACvCxE,SAAUoE,EAAWpE,SACrB4E,QAASR,EAAWQ,QACpBC,SAAUpB,EAAcW,EAAWS,QAAQ,CAC5C,IAEqB,IAAf3M,IACHkM,EAAa9R,EAEnB4R,EAAe,CACbzJ,MAAO2J,EAAWK,SAClBC,WAAYN,EAAWM,WACvBC,oBAAqB,CACnBJ,KAAMd,EAAcW,EAAWG,IAAI,EACnCC,OAAQf,EAAcW,EAAWI,MAAM,EACvCxE,SAAUoE,EAAWpE,SACrB4E,QAASR,EAAWQ,QACpBC,SAAUpB,EAAcW,EAAWS,QAAQ,CAC5C,IAGN,CACF,CAAA,GACEzG,MAAK,EAGRjD,MAAM9C,EAASsL,EAAW,EAC1BxI,MAAM9C,EAAS,sBAAsB,EACrC8C,MAAM9C,EAAS,WAAW,GAM1B6L,IAEA,IAGQS,EAHR,GAAKT,GAAiBA,EAAaS,oBAInC,OADQA,EAAwBT,EAAH,oBAEQ,UAAnC,OAAOA,EAAaQ,YACM,EAA1BR,EAAaQ,YACiB,UAA9B,OAAOR,EAAazJ,OACQ,EAA5ByJ,EAAazJ,MAAMqK,QACiB,UAApC,OAAOH,EAAoBJ,MACO,EAAlCI,EAAoBJ,KAAKO,QACa,UAAtC,OAAOH,EAAoBH,QACS,EAApCG,EAAoBH,OAAOM,QACa,UAAxC,OAAOH,EAAoB3E,UACW,EAAtC2E,EAAoB3E,SAAS8E,QACU,UAAvC,OAAOH,EAAoBC,SACU,EAArCD,EAAoBC,QAAQE,QACY,UAAxC,OAAOH,EAAoBE,UACW,EAAtCF,EAAoBE,SAASC,MAEjC,GA1B2BZ,CAAY,EAAIA,EAAe,IAC1D,CCnJO,IAAM5F,GAAgB,8BACvBC,GAAmB,EACnBC,EAAoB,2BAStBC,GAAuD,KAC3D,SAASC,KAeP,OAdKD,GAAAA,IACShH,EAAO6G,GAAeC,GAAkB,CAClD3G,QAAS,CAACmN,EAAW7M,KAMZ,IADCA,GAEJ6M,EAAUpG,kBAAkBH,CAAiB,CAElD,CACF,CAAA,CAGL,CAGOxF,eAAegM,GACpBC,GAEA,IAAM5S,EAAMmL,GAAOyH,CAAoB,EAEjCf,EAAgB,MADX/I,MAAMuD,MAEdpJ,YAAYkJ,CAAiB,EAC7BzJ,YAAYyJ,CAAiB,EAC7BhK,IAAInC,CAAG,EAEV,OAAI6R,KAIIgB,EAAkB/J,MAAM2I,GAC5BmB,EAAqBjJ,UAAU+H,QAAQ,IAGvC5I,MAAMgK,GAAMF,EAAsBC,CAAe,EAC1CA,GAFT,KAAA,EAKJ,CAGOlM,eAAemM,GACpBF,EACAf,GAEA,IAAM7R,EAAMmL,GAAOyH,CAAoB,EAEjCjP,GADKmF,MAAMuD,MACHpJ,YAAYkJ,EAAmB,WAAW,EAGxD,OAFArD,MAAMnF,EAAGjB,YAAYyJ,CAAiB,EAAEK,IAAIqF,EAAc7R,CAAG,EAC7D8I,MAAMnF,EAAGI,KACF8N,CACT,CAsBA,SAAS1G,GAAO,CAAExB,UAAAA,IAChB,OAAOA,EAAU0B,KACnB,CCvBO,IAAM7D,EAAgB,IAAInI,EAC/B,YACA,YArD4C,CAC5CoI,4BACE,kDACFsL,2BACE,gDACFC,uBACE,wDACFC,qBACE,qEACFC,qBACE,mEACFC,sBACE,2EACFC,yBACE,mGACFC,qCACE,+EACFC,yBACE,qEACFC,2BACE,2DACFC,2BACE,yEAEFC,sBACE,oEACFC,wBACE,wDACFC,yBACE,4IAEFC,0BACE,uEACFC,qBACE,iEACFC,oBAA+B,yCAC/BC,gCACE,wIAiBO,ECwBJpN,eAAeqN,GACpBpB,EACAxK,GAEA,IAEM6L,EAAqB,CACzBvN,OAAQ,SACRmD,QAJcf,MAAMM,GAAWwJ,CAAoB,GAOrD,IACE,IAMQhU,EAFFsV,EAA4BpL,MAJjBA,MAAMwF,MAClB6F,GAAYvB,EAAqBjJ,SAAS,EAA7C,IAAkDvB,EAClD6L,CAAkB,GAE6BlL,OACjD,GAAImL,EAAa5P,MAEf,MADM1F,EAAUsV,EAAa5P,MAAM1F,QAC7B4I,EAAclI,OAA2C,2BAAA,CAC7D8U,UAAWxV,CACZ,CAAA,CAMJ,CAJC,MAAOyV,GACP,MAAM7M,EAAclI,OAA2C,2BAAA,CAC7D8U,UAAYC,GAAeC,SAAU,CACtC,CAAA,CACF,CACH,CAEA,SAASH,GAAY,CAAElM,UAAAA,IACrB,OAAU6I,gBAAqB7I,iBACjC,CAEAtB,eAAeyC,GAAW,CACxBO,UAAAA,EACAmD,cAAAA,IAEA,IAAM2B,EAAY3F,MAAMgE,EAAc2C,WAEtC,OAAO,IAAInG,QAAQ,CACjBC,eAAgB,mBAChBC,OAAQ,mBACRC,iBAAkBE,EAAUN,OAC5BkL,qCAAsC,OAAO9F,CAC9C,CAAA,CACH,CAEA,SAAS+F,GAAQ,CACfrC,OAAAA,EACAD,KAAAA,EACAvE,SAAAA,EACA6E,SAAAA,CAAQ,GAER,IAAM5E,EAAuB,CAC3B6G,IAAK,CACH9G,SAAAA,EACAuE,KAAAA,EACAC,OAAAA,CACD,GAOH,OAJIK,IAAa3B,KACfjD,EAAK6G,IAAIC,kBAAoBlC,GAGxB5E,CACT,CCxJA,IAAM+G,GAAsB,OAErBhO,eAAeiO,GACpBC,GAEA,IAwIAC,EAGMC,EACAC,EACAC,EACAC,EA9IAC,EAAmBrM,MAmH3BnC,MACEyO,EACA5C,KAEA,IAAM6C,EAAevM,MAAMsM,EAAeE,YAAYC,gBAAe,EACrE,OAAIF,GAIGD,EAAeE,YAAYE,UAAU,CAC1CC,gBAAiB,CAAA,EAGjBC,sBL/I0BC,IAC5B,IACMC,GAAUD,EADA,IAAIE,QAAQ,EAAKF,EAAalD,OAAS,GAAM,CAAC,GAE3D5S,QAAQ,MAAO,GAAG,EAClBA,QAAQ,KAAM,GAAG,EAEdiW,EAAUC,KAAKH,CAAM,EACrBI,EAAc,IAAIxL,WAAWsL,EAAQrD,MAAM,EAEjD,IAAKxQ,IAAIgU,EAAI,EAAGA,EAAIH,EAAQrD,OAAQ,EAAEwD,EACpCD,EAAYC,GAAKH,EAAQI,WAAWD,CAAC,EAEvC,OAAOD,CACT,GKkIwCxD,CAAQ,CAC7C,CAAA,CACH,GAjIIqC,EAAUO,eACVP,EAAUrC,QAAS,EAGfF,EAA2C,CAC/CE,SAAUqC,EAAUrC,SACpBD,QAASsC,EAAUO,eAAgBe,MACnCxI,SAAUwH,EAAiBxH,SAC3BuE,KAAMd,EAAc+D,EAAiBhK,OAAO,MAAM,CAAE,EACpDgH,OAAQf,EAAc+D,EAAiBhK,OAAO,QAAQ,CAAE,GAGpD0G,EAAe/I,MAAM6J,GAAMkC,EAAUjC,oBAAoB,EAC/D,GAAKf,EAAL,CAGO,GAuHPiD,EAtHgBjD,EAAaS,oBAyHvByC,EAzH6CzC,EAyHZE,WAAasC,EAAUtC,SACxDwC,EA1H6C1C,EA0HZ3E,WAAamH,EAAUnH,SACxDsH,EA3H6C3C,EA2HhBJ,OAAS4C,EAAU5C,KAChDgD,EA5H6C5C,EA4HdH,SAAW2C,EAAU3C,OAEnD4C,GAAmBC,GAAmBC,GAAeC,EAhHrD,OAAIzM,KAAKC,IAAG,GAAMmJ,EAAaQ,WAAasC,IAwCrDhO,MACEkO,EACAhD,KAEA,IACE,IAAMuE,EAAetN,MDrClBnC,MACLiM,EACAf,KAEA,IAAMhI,EAAUf,MAAMM,GAAWwJ,CAAoB,EAC/ChF,EAAO4G,GAAQ3C,EAAaS,mBAAoB,EAEhD+D,EAAgB,CACpB3P,OAAQ,QACRmD,QAAAA,EACA+D,KAAMQ,KAAKC,UAAUT,CAAI,GAG3B3L,IAAIiS,EACJ,IACE,IAAM/L,EAAWW,MAAMwF,MAClB6F,GAAYvB,EAAqBjJ,SAAS,EAA7C,IAAkDkI,EAAazJ,MAC/DiO,CAAa,EAEfnC,EAAepL,MAAMX,EAASY,MAK/B,CAJC,MAAOsL,GACP,MAAM7M,EAAclI,OAAsC,sBAAA,CACxD8U,UAAYC,GAAeC,SAAU,CACtC,CAAA,CACF,CAED,GAAIJ,EAAa5P,MAEf,MADM1F,EAAUsV,EAAa5P,MAAM1F,QAC7B4I,EAAclI,OAAsC,sBAAA,CACxD8U,UAAWxV,CACZ,CAAA,EAGH,GAAKsV,EAAa9L,MAIlB,OAAO8L,EAAa9L,MAHlB,MAAMZ,EAAclI,OAAM,wBAI9B,GCAMuV,EAAUjC,qBACVf,CAAY,EAGRyE,EAAoC,CACxC,GAAGzE,EACHzJ,MAAOgO,EACP/D,WAAY5J,KAAKC,IAAK,GAIxB,OADAI,MAAMgK,GAAM+B,EAAUjC,qBAAsB0D,CAAmB,EACxDF,CAGR,CAFC,MAAO7X,GACP,MAAMA,CACP,CACH,GA3DuBsW,EAAW,CAC5BzM,MAAOyJ,EAAazJ,MACpBiK,WAAY5J,KAAKC,IAAK,EACtB4J,oBAAAA,CACD,CAAA,EAGMT,EAAazJ,MApBpB,IACEU,MAAMkL,GACJa,EAAUjC,qBACVf,EAAazJ,KAAK,CAKrB,CAHC,MAAO7J,GAEPiS,QAAQ+F,KAAKhY,CAAC,CACf,CAaF,CA1BC,OAAOiY,GAAY3B,EAAUjC,qBAAsBN,CAAmB,CA2B1E,CAMO3L,eAAe8P,GACpB5B,GAEA,IHQMlR,EGRAkO,EAAe/I,MAAM6J,GAAMkC,EAAUjC,oBAAoB,EAUzDuC,GATFtD,IACF/I,MAAMkL,GACJa,EAAUjC,qBACVf,EAAazJ,KAAK,EHEhBpI,EAAMmL,GGAK0J,EAAUjC,oBHAY,EAGvC9J,MADMnF,GADKmF,MAAMuD,MACHpJ,YAAYkJ,EAAmB,WAAW,GAC/CzJ,YAAYyJ,CAAiB,EAAEO,OAAO1M,CAAG,EAClD8I,MAAMnF,EAAGI,MGCP+E,MAAM+L,EAAUO,eAAgBE,YAAYC,gBAAe,GAC7D,MAAIJ,CAAAA,GACKA,EAAiBuB,aAK5B,CAyBA/P,eAAe6P,GACb5D,EACAN,GAEA,IAIMT,EAA6B,CACjCzJ,MALYU,MDnGTnC,MACLiM,EACAN,KAEA,IAAMzI,EAAUf,MAAMM,GAAWwJ,CAAoB,EAC/ChF,EAAO4G,GAAQlC,CAAmB,EAElCqE,EAAmB,CACvBjQ,OAAQ,OACRmD,QAAAA,EACA+D,KAAMQ,KAAKC,UAAUT,CAAI,GAG3B3L,IAAIiS,EACJ,IACE,IAAM/L,EAAWW,MAAMwF,MACrB6F,GAAYvB,EAAqBjJ,SAAS,EAC1CgN,CAAgB,EAElBzC,EAAepL,MAAMX,EAASY,MAK/B,CAJC,MAAOsL,GACP,MAAM7M,EAAclI,OAAyC,yBAAA,CAC3D8U,UAAYC,GAAeC,SAAU,CACtC,CAAA,CACF,CAED,GAAIJ,EAAa5P,MAEf,MADM1F,EAAUsV,EAAa5P,MAAM1F,QAC7B4I,EAAclI,OAAyC,yBAAA,CAC3D8U,UAAWxV,CACZ,CAAA,EAGH,GAAKsV,EAAa9L,MAIlB,OAAO8L,EAAa9L,MAHlB,MAAMZ,EAAclI,OAAM,2BAI9B,GC8DIsT,EACAN,CAAmB,EAInBD,WAAY5J,KAAKC,IAAK,EACtB4J,oBAAAA,GAGF,OADAxJ,MAAMgK,GAAMF,EAAsBf,CAAY,EACvCA,EAAazJ,KACtB,CE5G0B0P,IAkNIC,GAjN5B,uBAiNwCC,GAhNxC,sBAiNMC,GAAc,GACpB,IAAKhW,IAAIgU,EAAI,EAAGA,EAAI8B,GAAGtF,OAAQwD,CAAC,GAC9BgC,GAAYC,KAAKH,GAAGI,OAAOlC,CAAC,CAAC,EACzBA,EAAI+B,GAAGvF,QACTwF,GAAYC,KAAKF,GAAGG,OAAOlC,CAAC,CAAC,EAtG5BtP,eAAe8U,GACpB5G,EACAgC,GAEA,IAAM6E,GAQR,CACE7E,EACApM,KAEA,IAAMiR,EAAW,GAkCjB,OA9BM7E,EAAgBI,OACpByE,EAASC,eAAiB9E,EAAgBI,MAGtCJ,EAAgBQ,eACpBqE,EAASpB,WAAazD,EAAgBQ,cAGxCqE,EAASE,YAAcnR,EAGrBiR,EAASG,cADLhF,EAAgBS,aACI/P,EAAYuU,qBAEZvU,EAAYwU,cAFqBzH,SAAQ,EAKnEoH,EAASM,aAAeT,GAAiBjH,WACzCoH,EAASO,aAAevR,KAAKwR,OAAOrc,QAAQ,gBAAiB,EAAE,EAEzDgX,EAAgBM,eACpBuE,EAASvE,aAAeN,EAAgBM,cAG1CuE,EAAS9V,MAAQ4V,GAAwBlH,WAEnCuC,EAAgBa,YAAYG,kBAChC6D,EAAS7D,gBAAkBhB,EAAgBa,YAAYG,iBAIlD6D,CACT,GA9CI7E,EACA/N,MAAM+L,EAAUjC,qBAAqB9F,cAAcyD,MAAK,CAAE,EAiD5DmL,EA9CoCA,EA+CpCS,EA/C8CtF,EAAgBsF,UAiDxD9B,EAAW,GAGjBA,EAAS+B,cAAgB1B,KAAKC,MAAMlS,KAAKC,IAAK,CAAA,EAAE4L,WAChD+F,EAASgC,6BAA+BjO,KAAKC,UAAU,CACrDiO,uBAAwBZ,CACzB,CAAA,EAEKS,IACJ9B,EAASkC,iBAOgBJ,IACY,CACrCK,gBAAiB,CACfC,SAAU,CACRC,6BAA8BP,CAC/B,CACF,KAb8CA,CAAS,GAI1DtH,EAAU6D,UAAUR,KAAKmC,CAAQ,CA7DnC,Cc5FO1T,eAAegW,GACpB/W,EACAiP,GAEA,IAAMgC,GA4GR,CAAmC,CACjCnX,KAAAA,MAEA,GAAI,CAACA,EACH,OAAO,KAGT,IACE,OAAOA,EAAKqJ,MAIb,CAHC,MAAOsL,GAEP,OAAO,IACR,CACH,GAzHoDzO,CAAK,EACvD,GAAKiR,EAAL,CAMIhC,EAAU0D,0CACZzP,MAAM2S,GAAS5G,EAAWgC,CAAe,EAI3C,IfNAC,EACAC,EAqBMC,EehBA4F,EAAa9T,MAAM+T,KACzB,GAAsBD,EAqIJrb,KAChBub,GAC6B,YAA3BA,EAAOC,iBAGP,CAACD,EAAOE,IAAIC,WAAW,qBAAqB,CAAC,EA1IjD,CACSC,IAoJEJ,EANXF,EA9I6CA,EA+I7C/F,EA/IyDA,EAiJzDA,EAAgBiE,oBAAsB,CAAA,EACtCjE,EAAgBoD,YAAc1S,EAAY6S,cAE1C,IAAW0C,KAAUF,EACnBE,EAAOjR,YAAYgL,CAAe,CApJnC,MAGKA,EAAgBS,cACpBxO,MA6JFqU,IAIA,IAAQC,EAAYD,EAAH,QACTE,EAAe1D,aAAH,WAOpB,OANIyD,GAAWC,GAAcD,EAAQ3K,OAAS4K,GAC5C7M,QAAQ+F,mCACwB8G,yDAAkE,EAI7F3S,KAAKsO,aAAasE,iBACVH,EAA4B5F,OAAS,GAClD4F,CAA2B,CAE/B,KAlGQI,EAAsD,CAC1D,IAHF1G,EAzE6CA,GA4EvBS,eAMC5X,KAAO,EAC3B4b,IAAUzE,GAGN0G,EAtFsD,EAGxD1I,GAICA,EAAU2D,6Bf9EV1B,EAA0B,CAC9BG,MAHFJ,EeiFqCA,Gf9EbI,KAEtBC,YAAaL,EAAgBM,aAE7BC,UAAWP,EAAgBQ,cAW7BP,EAR6BA,GAS7BC,EATsCF,GAWVS,eAI5BR,EAAQQ,aAAe,IAEjBC,EAAQR,EAAuBO,aAAcC,SAEjDT,EAAQQ,aAAcC,MAAQA,IAG1B3J,EAAOmJ,EAAuBO,aAAc1J,QAEhDkJ,EAAQQ,aAAc1J,KAAOA,IAGzB4J,EAAQT,EAAuBO,aAAcE,SAEjDV,EAAQQ,aAAcE,MAAQA,GAG1BC,EAAOV,EAAuBO,aAAcG,QAEhDX,EAAQQ,aAAcG,KAAOA,GAK/BX,EAtCqBA,GAuCrBC,EAvC8BF,GAyCFnX,OAI5BoX,EAAQpX,KAAOqX,EAAuBrX,MAItCoX,EAhDoBA,IAiDpBC,EAjD6BF,GAqDHa,YACvBX,EAAuBO,cAAcK,gBAKxCb,EAAQY,WAAa,IAEfE,EACJb,EAAuBW,YAAYE,MACnCb,EAAuBO,cAAcK,gBAGrCb,EAAQY,WAAYE,KAAOA,GAIvBZ,EAAiBD,EAAuBW,YAAYG,mBAExDf,EAAQY,WAAYV,eAAiBA,GeH/BF,EfnEDA,EeqE+C,YAAhD,OAAOjC,EAAU2D,2BACnB1P,MAAM+L,EAAU2D,2BAA2B1B,CAAO,EAElDjC,EAAU2D,2BAA2BuC,KAAKjE,CAAO,EA5BpD,CA+BH,CAEOnQ,eAAe6W,GACpB5X,GAEA,IAAMiR,EACJjR,EAAM0R,cAAc5X,OAAO4b,IAE7B,GAAKzE,GAEMjR,CAAAA,EAAM6X,OAAV,CAOP7X,EAAM8X,yBAAwB,EAC9B9X,EAAM0R,aAAavL,QAGnB,IAAM6L,GA0ISd,IAEf,IAAMc,EAAOd,EAAQY,YAAYE,MAAQd,EAAQQ,cAAcK,aAC/D,OAAIC,KL5P2BlY,GAER,UAAhB,OAAOA,GAAuBA,GAAQqR,MAAuBrR,GK8P/CoX,EAAQpX,IAAI,EAExBgL,KAAKiT,SAASzB,OAEd,KAEX,GAvJuBrF,CAAe,EACpC,GAAKe,EAAL,CAKA,ICvHoB1N,EDuHd8S,EAAM,IAAIY,IAAIhG,EAAMlN,KAAKiT,SAASE,IAAI,EACtCC,EAAY,IAAIF,IAAIlT,KAAKiT,SAASzB,MAAM,EAE9C,GAAIc,EAAIe,OAASD,EAAUC,KAA3B,CAIA9b,IAAI6a,EAAShU,MA0DfnC,MAA+BqW,IAC7B,IAEWF,EAAX,IAAWA,KAFQhU,MAAM+T,KAEQ,CAC/B,IAAMmB,EAAY,IAAIJ,IAAId,EAAOE,IAAKtS,KAAKiT,SAASE,IAAI,EAExD,GAAIb,EAAIe,OAASC,EAAUD,KACzB,OAAOjB,CAEV,CAED,OAAO,IACT,GAtEqCE,CAAG,EAYtC,GAVKF,EAOHA,EAAShU,MAAMgU,EAAOmB,SANtBnB,EAAShU,MAAM4B,KAAKwT,QAAQC,WAAWvG,CAAI,ECjIzB1N,EDqIN,IAAZpB,MCpIK,IAAI9E,QAAcC,IACvBkG,WAAWlG,EAASiG,CAAE,CACxB,CAAC,GDuII4S,EAOL,OAFAjG,EAAgBoD,YAAc1S,EAAY4S,qBAC1CtD,EAAgBiE,oBAAsB,CAAA,EAC/BgC,EAAOjR,YAAYgL,CAAe,CArBxC,CARA,CAVA,CAwCH,CA8EA,SAASgG,KACP,OAAOnS,KAAKwT,QAAQE,SAAS,CAC3B5d,KAAM,SACN6d,oBAAqB,CAAA,CAEtB,CAAA,CACH,CbhMA,SAAS1O,GAAqBC,GAC5B,OAAOpI,EAAclI,OAA4C,4BAAA,CAC/DsQ,UAAAA,CACD,CAAA,CACH,CDuMSqI,GAAYG,KAAK,EAAE,QExOfC,GAoBX3Z,YACEqR,EACAjD,EACAwL,GAhBFvZ,KAAwCwZ,yCAAY,CAAA,EAEpDxZ,KAA0ByZ,2BAGf,KAEXzZ,KAAgB0Z,iBACd,KAEF1Z,KAAS2Z,UAAe,GACxB3Z,KAAmB4Z,oBAAY,CAAA,EAO7B,IAAMhP,GD7BuBoG,IAC/B,GAAI,CAACA,GAAO,CAACA,EAAIG,QACf,MAAMP,GAAqB,0BAA0B,EAGvD,GAAI,CAACI,EAAI/Q,KACP,MAAM2Q,GAAqB,UAAU,EAIvC,IAQWQ,EADHD,EAAYH,EAAH,QACjB,IAAWI,IAR8C,CACvD,YACA,SACA,QACA,qBAKA,GAAI,CAACD,EAAQC,GACX,MAAMR,GAAqBQ,CAAO,EAItC,MAAO,CACL/E,QAAS2E,EAAI/Q,KACbiJ,UAAWiI,EAAQjI,UACnBoB,OAAQ6G,EAAQ7G,OAChBgC,MAAO6E,EAAQ7E,MACfqG,SAAUxB,EAAQ0I,kBAEtB,GCFuC7I,CAAG,EAEtChR,KAAK6T,qBAAuB,CAC1B7C,IAAAA,EACApG,UAAAA,EACAmD,cAAAA,EACAwL,kBAAAA,EAEH,CAEDjI,UACE,OAAOrM,QAAQC,SAChB,CACF,CQKD,IAAMqa,GAAmD,IAGvD,IAAMzJ,EAAY,IAAIwD,GACpBrI,EAAUC,YAAY,KAAK,EAAEnC,aAAc,EAC3CkC,EAAUC,YAAY,wBAAwB,EAAEnC,aAAY,EAC5DkC,EAAUC,YAAY,oBAAoB,CAAC,EAa7C,OAVAvF,KAAKlG,iBAAiB,OAAQjG,IAC5BA,EAAEggB,UAAU5B,GAAOpe,EAAGsW,CAA6B,CAAC,CACtD,CAAC,EACDnK,KAAKlG,iBAAiB,yBAA0BjG,IAC9CA,EAAEggB,WI/BC5X,MACLf,EACAiP,KAEA,IAAQ2J,GACHA,EADuB5Y,EAAH,kBAOnBiM,EAAe/I,MAAM6J,GAAMkC,EAAUjC,oBAAoB,EAC/D9J,MAAM2N,GAAoB5B,CAAS,EAEnCA,EAAUrC,SACRX,GAAcS,qBAAqBE,UAAY3B,GACjD/H,MAAM8L,GAAiBC,CAAS,GAT9B/L,MAAM2N,GAAoB5B,CAAS,CAUvC,GJc4BtW,EAAGsW,CAA6B,CAAC,CAC3D,CAAC,EACDnK,KAAKlG,iBAAiB,oBAAqBjG,IACzCA,EAAEggB,UAAUf,GAAoBjf,CAAC,CAAC,CACpC,CAAC,EAEMsW,CACT,EC0EgB,SAAA4J,GACd5J,EACAuG,GAGOsD,IK7IP7J,EL4IAA,EAAYzU,EAAmByU,CAAS,EK3IxCuG,EL4I2DA,EK1I3D,GAAsB3Y,KAAAA,IAAlBiI,KAAKiU,SACP,MAAMnX,EAAclI,OAAM,wBAK5B,OAFAuV,EAAU2D,2BAA6B4C,EAEhC,KACLvG,EAAU2D,2BAA6B,IACzC,CLmIF,CDtDE/H,GAAkBA,mBAChB,IAAInQ,EAAU,eAAgBge,GAAyC,QAAA,CAAA,QOpB9DM,GACXlgB,YAAqBqR,EAAyB1P,GAAzBtB,KAAGgR,IAAHA,EAAyBhR,KAASsB,UAATA,EAC5CtB,KAAKgR,IAAMA,EACXhR,KAAKsB,UAAYA,CAClB,CAEDoP,eAAeS,GAIb,ONCGvJ,MACLkO,EACA3E,IAGO2O,GADPhK,EAAYzU,EAAmByU,CAAS,EACQ3E,CAAO,GMNrCnR,KAAKsB,UAAW6P,CAAO,CACxC,CAEDgL,oBACE,OAAOA,GAAYnc,KAAKsB,SAAS,CAClC,CAED8a,UACEC,GAEA,OAAOD,GAAUpc,KAAKsB,UAAW+a,CAAc,CAChD,CAEDqD,oBACErD,GAEA,OAAOqD,GAAoB1f,KAAKsB,UAAW+a,CAAc,CAC1D,CACF,CC7FD,IAAM0D,GAA8D,GAG9DpU,MAAQ,6BAA8BA,KAEjC,IAAIkU,GACT5O,EAAUC,YAAY,YAAY,EAAEnC,eACpCkC,EAAUC,YAAY,cAAc,EAAEnC,aAAc,CAAA,EAI/C,IAAI8Q,GACT5O,EAAUC,YAAY,YAAY,EAAEnC,eACpCkC,EAAUC,YAAY,WAAW,EAAEnC,aAAc,CAAA,EAKjDiR,GAAoB,CACxBC,YDIc,WACd,OAAItU,MAAQ,6BAA8BA,KAiCxCrM,EAAsB,GACtB,gBAAiBqM,MACjB,iBAAkBA,MAClBqP,0BAA0B5a,UAAU8f,eAAe,kBAAkB,GACrEC,iBAAiB/f,UAAU8f,eAAe,QAAQ,EArBhC,aAAlB,OAAOE,QACP9gB,EAAsB,GjDsKpB,EAAqB,aAArB,OAAOiP,WAA8BA,CAAAA,UAAU8R,gBiDpKjD,kBAAmB9R,WACnB,gBAAiB6R,QACjB,iBAAkBA,QAClB,UAAWA,QACXpF,0BAA0B5a,UAAU8f,eAAe,kBAAkB,GACrEC,iBAAiB/f,UAAU8f,eAAe,QAAQ,CAjBtD,GCRGI,EAAAA,QAAgCC,SAASC,kBACxC,IAAIjf,EACF,mBACAwe,WAED,EAAC9d,gBAAgB+d,EAAiB,CAAC,ECpCxCM,EAAAA,QAAS3O,qDAA6B"}