'use client';

import React from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { MainLayout } from '@/components/layout/MainLayout';

export default function AthleteConnections() {
  return (
    <ProtectedRoute allowedRoles={['PLAYER']}>
      <MainLayout>
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Trainer-Verbindungen
            </h1>
            <p className="mt-2 text-gray-600">
              Verwalten Sie Ihre Verbindungen zu Trainern und Datenschutz-Einstellungen
            </p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-gray-600">
              Trainer-Verbindungen werden in den nächsten Entwicklungsschritten implementiert.
            </p>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
