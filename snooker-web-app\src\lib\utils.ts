import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import moment from 'moment';
import {
  TrainingRecord,
  ExerciseRecord,
  TrainingsplanHistory,
  QuestionRecord,
  AthleteStats,
  ChartDataPoint
} from '@/types';

// Utility function for combining Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Date formatting utilities
export const formatDate = (timestamp: number | Date, format: string = 'DD.MM.YYYY'): string => {
  return moment(timestamp).format(format);
};

export const formatDateTime = (timestamp: number | Date): string => {
  return moment(timestamp).format('DD.MM.YYYY HH:mm');
};

export const formatRelativeTime = (timestamp: number | Date): string => {
  moment.locale('de');
  return moment(timestamp).fromNow();
};

// Data processing utilities
export const calculateExerciseCompletionRate = (exercises: ExerciseRecord[]): number => {
  if (exercises.length === 0) return 0;
  const completed = exercises.filter(ex => ex.completed).length;
  return Math.round((completed / exercises.length) * 100);
};

export const calculateTrainingsplanAdherence = (plans: TrainingsplanHistory[]): number => {
  if (plans.length === 0) return 0;
  
  const latestPlan = plans[0];
  if (!latestPlan.items || latestPlan.items.length === 0) return 0;
  
  const checkedItems = latestPlan.items.filter(item => item.isChecked).length;
  return Math.round((checkedItems / latestPlan.items.length) * 100);
};

export const calculateSelfAssessmentScore = (records: TrainingRecord[]): number => {
  if (records.length === 0) return 0;
  
  let totalScore = 0;
  let scoreCount = 0;
  
  records.forEach(record => {
    if (record.items && record.items.length > 0) {
      record.items.forEach(item => {
        if (item.score) {
          totalScore += item.score;
          scoreCount++;
        }
      });
    }
  });
  
  return scoreCount > 0 ? Math.round((totalScore / scoreCount) * 10) / 10 : 0;
};

export const getLastActivityDate = (
  trainingRecords: TrainingRecord[],
  exerciseRecords: ExerciseRecord[],
  planRecords: TrainingsplanHistory[],
  questionRecords: QuestionRecord[]
): string => {
  const allTimestamps = [
    ...trainingRecords.map(r => r.lastUpdated),
    ...exerciseRecords.map(r => r.lastUpdated),
    ...planRecords.map(r => r.lastUpdated),
    ...questionRecords.map(r => r.lastUpdated)
  ];
  
  if (allTimestamps.length === 0) return 'Keine Aktivität';
  
  const latestTimestamp = Math.max(...allTimestamps);
  return formatRelativeTime(latestTimestamp);
};

// Chart data preparation
export const prepareExerciseCompletionChart = (
  exercises: ExerciseRecord[],
  days: number = 30
): ChartDataPoint[] => {
  const endDate = moment();
  const startDate = moment().subtract(days, 'days');
  const chartData: ChartDataPoint[] = [];
  
  // Group exercises by date
  const exercisesByDate = exercises.reduce((acc, exercise) => {
    const date = moment(exercise.timestamp).format('DD.MM.YYYY');
    if (!acc[date]) acc[date] = [];
    acc[date].push(exercise);
    return acc;
  }, {} as Record<string, ExerciseRecord[]>);
  
  // Generate data points for each day
  for (let date = startDate.clone(); date.isSameOrBefore(endDate); date.add(1, 'day')) {
    const dateStr = date.format('DD.MM.YYYY');
    const dayExercises = exercisesByDate[dateStr] || [];
    const completionRate = calculateExerciseCompletionRate(dayExercises);
    
    chartData.push({
      date: dateStr,
      value: completionRate
    });
  }
  
  return chartData;
};

export const prepareSelfAssessmentChart = (
  records: TrainingRecord[],
  days: number = 30
): ChartDataPoint[] => {
  const endDate = moment();
  const startDate = moment().subtract(days, 'days');
  const chartData: ChartDataPoint[] = [];
  
  // Group records by date
  const recordsByDate = records.reduce((acc, record) => {
    const date = moment(record.lastUpdated).format('DD.MM.YYYY');
    if (!acc[date]) acc[date] = [];
    acc[date].push(record);
    return acc;
  }, {} as Record<string, TrainingRecord[]>);
  
  // Generate data points for each day
  for (let date = startDate.clone(); date.isSameOrBefore(endDate); date.add(1, 'day')) {
    const dateStr = date.format('DD.MM.YYYY');
    const dayRecords = recordsByDate[dateStr] || [];
    const avgScore = calculateSelfAssessmentScore(dayRecords);
    
    chartData.push({
      date: dateStr,
      value: avgScore
    });
  }
  
  return chartData;
};

// Search and filter utilities
export const filterRecordsByDateRange = <T extends { lastUpdated: number }>(
  records: T[],
  startDate: Date,
  endDate: Date
): T[] => {
  const start = startDate.getTime();
  const end = endDate.getTime();
  
  return records.filter(record => 
    record.lastUpdated >= start && record.lastUpdated <= end
  );
};

export const searchRecords = <T extends Record<string, any>>(
  records: T[],
  query: string,
  searchFields: (keyof T)[]
): T[] => {
  if (!query.trim()) return records;
  
  const lowercaseQuery = query.toLowerCase();
  
  return records.filter(record =>
    searchFields.some(field => {
      const value = record[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(lowercaseQuery);
      }
      return false;
    })
  );
};

// Exercise category utilities
export const getExerciseCategoryDisplayName = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'potting': 'Lochen',
    'safety': 'Sicherheit',
    'break_building': 'Break Building',
    'technique': 'Technik',
    'mental': 'Mental',
    'physical': 'Körperlich'
  };
  
  return categoryMap[category] || category;
};

// Statistics calculation
export const calculateAthleteStats = (
  trainingRecords: TrainingRecord[],
  exerciseRecords: ExerciseRecord[],
  planRecords: TrainingsplanHistory[],
  questionRecords: QuestionRecord[]
): AthleteStats => {
  return {
    exerciseCompletionRate: calculateExerciseCompletionRate(exerciseRecords),
    trainingsplanAdherence: calculateTrainingsplanAdherence(planRecords),
    selfAssessmentScore: calculateSelfAssessmentScore(trainingRecords),
    questionRecordsCount: questionRecords.length,
    lastActivity: getLastActivityDate(trainingRecords, exerciseRecords, planRecords, questionRecords)
  };
};

// Data validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPassword = (password: string): boolean => {
  return password.length >= 6;
};

// Error message utilities
export const getFirebaseErrorMessage = (errorCode: string): string => {
  const errorMessages: Record<string, string> = {
    'auth/user-not-found': 'Benutzer nicht gefunden.',
    'auth/wrong-password': 'Falsches Passwort.',
    'auth/email-already-in-use': 'E-Mail-Adresse wird bereits verwendet.',
    'auth/weak-password': 'Passwort ist zu schwach.',
    'auth/invalid-email': 'Ungültige E-Mail-Adresse.',
    'auth/too-many-requests': 'Zu viele Anfragen. Bitte versuchen Sie es später erneut.',
    'permission-denied': 'Keine Berechtigung für diese Aktion.',
    'not-found': 'Dokument nicht gefunden.',
    'unavailable': 'Service vorübergehend nicht verfügbar.'
  };
  
  return errorMessages[errorCode] || 'Ein unbekannter Fehler ist aufgetreten.';
};

// Format date for chart labels
export const formatChartDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('de-DE', {
    day: '2-digit',
    month: '2-digit'
  });
};

// Generate mock training data for charts
export const generateMockTrainingData = (
  athleteId?: string,
  dateRange?: { start: string; end: string }
) => {
  const days = 30; // Last 30 days
  const dates: Date[] = [];
  const progressScores: number[] = [];
  const selfAssessmentScores: number[] = [];
  const exerciseCompletionRates: number[] = [];

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    dates.push(date);

    // Generate realistic mock data with some progression
    const baseProgress = 60 + (days - i) * 0.8 + Math.random() * 10;
    const baseSelfAssessment = 55 + (days - i) * 0.6 + Math.random() * 15;
    const baseCompletion = 70 + (days - i) * 0.5 + Math.random() * 12;

    progressScores.push(Math.min(95, Math.max(40, Math.round(baseProgress))));
    selfAssessmentScores.push(Math.min(90, Math.max(35, Math.round(baseSelfAssessment))));
    exerciseCompletionRates.push(Math.min(100, Math.max(50, Math.round(baseCompletion))));
  }

  return {
    dates,
    progressScores,
    selfAssessmentScores,
    exerciseCompletionRates
  };
};
