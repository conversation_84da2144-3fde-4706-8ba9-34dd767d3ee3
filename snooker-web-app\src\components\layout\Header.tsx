'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { isTrainer, getRoleDisplayName } from '@/lib/auth';
import { 
  Menu, 
  X, 
  User, 
  LogOut, 
  BarChart3, 
  Users, 
  Settings,
  Home
} from 'lucide-react';

export const Header: React.FC = () => {
  const { user, userProfile, signOut } = useAuth();
  const router = useRouter();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const getNavigationItems = () => {
    if (!userProfile) return [];

    const baseItems = [
      {
        name: 'Dashboard',
        href: isTrainer(userProfile.role) ? '/trainer/dashboard' : '/athlete/dashboard',
        icon: Home
      }
    ];

    if (isTrainer(userProfile.role)) {
      return [
        ...baseItems,
        {
          name: 'Athleten',
          href: '/trainer/athletes',
          icon: Users
        },
        {
          name: 'Statistiken',
          href: '/trainer/statistics',
          icon: BarChart3
        }
      ];
    } else {
      return [
        ...baseItems,
        {
          name: 'Meine Daten',
          href: '/athlete/data',
          icon: BarChart3
        },
        {
          name: 'Trainer-Verbindungen',
          href: '/athlete/connections',
          icon: Users
        },
        {
          name: 'Einstellungen',
          href: '/athlete/settings',
          icon: Settings
        }
      ];
    }
  };

  const navigationItems = getNavigationItems();

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Title */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <h1 className="text-xl font-bold text-gray-900">
                Die Snooker App
              </h1>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => router.push(item.href)}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {item.name}
                </button>
              );
            })}
          </nav>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {user && userProfile && (
              <>
                <div className="hidden md:flex items-center space-x-2 text-sm text-gray-700">
                  <User className="h-4 w-4" />
                  <span>{userProfile.displayName}</span>
                  <span className="text-gray-500">({getRoleDisplayName(userProfile.role)})</span>
                </div>
                
                <button
                  onClick={handleSignOut}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-red-600 hover:bg-gray-50 rounded-md transition-colors"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  <span className="hidden md:inline">Abmelden</span>
                </button>
              </>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.name}
                    onClick={() => {
                      router.push(item.href);
                      setMobileMenuOpen(false);
                    }}
                    className="flex items-center w-full px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </button>
                );
              })}
              
              {user && userProfile && (
                <>
                  <div className="px-3 py-2 border-t border-gray-200 mt-2 pt-2">
                    <div className="flex items-center space-x-2 text-sm text-gray-700 mb-2">
                      <User className="h-4 w-4" />
                      <span>{userProfile.displayName}</span>
                    </div>
                    <div className="text-xs text-gray-500 mb-3">
                      {getRoleDisplayName(userProfile.role)}
                    </div>
                    <button
                      onClick={handleSignOut}
                      className="flex items-center w-full px-3 py-2 text-base font-medium text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    >
                      <LogOut className="h-5 w-5 mr-3" />
                      Abmelden
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};
