module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/firebase.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "auth": ()=>auth,
    "db": ()=>db,
    "default": ()=>__TURBOPACK__default__export__
});
(()=>{
    const e = new Error("Cannot find module 'firebase/app'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
(()=>{
    const e = new Error("Cannot find module 'firebase/auth'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
(()=>{
    const e = new Error("Cannot find module 'firebase/firestore'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
;
;
;
// Firebase configuration - matches the existing Android app configuration
const firebaseConfig = {
    apiKey: "AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY",
    authDomain: "die-snooker-app.firebaseapp.com",
    projectId: "die-snooker-app",
    storageBucket: "die-snooker-app.firebasestorage.app",
    messagingSenderId: "547283642216",
    appId: "1:547283642216:web:7f2fdc23dab5ce8430d8dd",
    measurementId: "G-GTFVZZ4LJ"
};
// Initialize Firebase only if it hasn't been initialized already
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const auth = getAuth(app);
const db = getFirestore(app);
const __TURBOPACK__default__export__ = app;
}),
"[project]/src/lib/firestore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createConnection": ()=>createConnection,
    "createUserProfile": ()=>createUserProfile,
    "deleteConnection": ()=>deleteConnection,
    "getExerciseDefinitions": ()=>getExerciseDefinitions,
    "getExerciseRecords": ()=>getExerciseRecords,
    "getQuestionRecords": ()=>getQuestionRecords,
    "getTrainerConnections": ()=>getTrainerConnections,
    "getTrainingRecords": ()=>getTrainingRecords,
    "getTrainingsplanHistory": ()=>getTrainingsplanHistory,
    "getUserConnections": ()=>getUserConnections,
    "getUserProfile": ()=>getUserProfile,
    "subscribeToTrainingRecords": ()=>subscribeToTrainingRecords,
    "subscribeToUserProfile": ()=>subscribeToUserProfile,
    "updateConnection": ()=>updateConnection,
    "updateUserProfile": ()=>updateUserProfile
});
(()=>{
    const e = new Error("Cannot find module 'firebase/firestore'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase.ts [app-ssr] (ecmascript)");
;
;
const getUserProfile = async (userId)=>{
    try {
        const docRef = doc(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_profiles', userId);
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
            return docSnap.data();
        }
        return null;
    } catch (error) {
        console.error('Error getting user profile:', error);
        throw error;
    }
};
const createUserProfile = async (userId, displayName, email, role = 'PLAYER')=>{
    try {
        const userProfile = {
            userId,
            displayName,
            email,
            role,
            emailVerified: false,
            registrationDate: Timestamp.now(),
            lastUpdated: Timestamp.now()
        };
        await setDoc(doc(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_profiles', userId), userProfile);
    } catch (error) {
        console.error('Error creating user profile:', error);
        throw error;
    }
};
const updateUserProfile = async (userId, updates)=>{
    try {
        const docRef = doc(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_profiles', userId);
        await updateDoc(docRef, {
            ...updates,
            lastUpdated: serverTimestamp()
        });
    } catch (error) {
        console.error('Error updating user profile:', error);
        throw error;
    }
};
const getUserConnections = async (userId)=>{
    try {
        const q = query(collection(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_connections'), where('initiatorId', '==', userId));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map((doc1)=>({
                id: doc1.id,
                ...doc1.data()
            }));
    } catch (error) {
        console.error('Error getting user connections:', error);
        throw error;
    }
};
const getTrainerConnections = async (trainerId)=>{
    try {
        const q = query(collection(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_connections'), where('targetId', '==', trainerId), where('status', '==', 'ACTIVE'));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map((doc1)=>({
                id: doc1.id,
                ...doc1.data()
            }));
    } catch (error) {
        console.error('Error getting trainer connections:', error);
        throw error;
    }
};
const createConnection = async (connection)=>{
    try {
        const connectionId = `${connection.initiatorId}_${connection.targetId}`;
        await setDoc(doc(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_connections', connectionId), connection);
    } catch (error) {
        console.error('Error creating connection:', error);
        throw error;
    }
};
const updateConnection = async (connectionId, updates)=>{
    try {
        const docRef = doc(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_connections', connectionId);
        await updateDoc(docRef, {
            ...updates,
            lastUpdated: Date.now()
        });
    } catch (error) {
        console.error('Error updating connection:', error);
        throw error;
    }
};
const deleteConnection = async (connectionId)=>{
    try {
        await deleteDoc(doc(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_connections', connectionId));
    } catch (error) {
        console.error('Error deleting connection:', error);
        throw error;
    }
};
const getTrainingRecords = async (userId, limitCount)=>{
    try {
        const constraints = [
            where('userId', '==', userId),
            orderBy('lastUpdated', 'desc')
        ];
        if (limitCount) {
            constraints.push(limit(limitCount));
        }
        const q = query(collection(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'training_records'), ...constraints);
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map((doc1)=>({
                id: doc1.id,
                ...doc1.data()
            }));
    } catch (error) {
        console.error('Error getting training records:', error);
        throw error;
    }
};
const getExerciseRecords = async (userId, limitCount)=>{
    try {
        const constraints = [
            where('userId', '==', userId),
            orderBy('lastUpdated', 'desc')
        ];
        if (limitCount) {
            constraints.push(limit(limitCount));
        }
        const q = query(collection(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'exercise_records'), ...constraints);
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map((doc1)=>({
                id: doc1.id,
                ...doc1.data()
            }));
    } catch (error) {
        console.error('Error getting exercise records:', error);
        throw error;
    }
};
const getExerciseDefinitions = async (userId)=>{
    try {
        const q = query(collection(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_exercise_definitions'), where('userId', '==', userId));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map((doc1)=>({
                id: doc1.id,
                ...doc1.data()
            }));
    } catch (error) {
        console.error('Error getting exercise definitions:', error);
        throw error;
    }
};
const getTrainingsplanHistory = async (userId, limitCount)=>{
    try {
        const constraints = [
            where('userId', '==', userId),
            orderBy('lastUpdated', 'desc')
        ];
        if (limitCount) {
            constraints.push(limit(limitCount));
        }
        const q = query(collection(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'trainingsplan_history'), ...constraints);
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map((doc1)=>({
                id: doc1.id,
                ...doc1.data()
            }));
    } catch (error) {
        console.error('Error getting trainingsplan history:', error);
        throw error;
    }
};
const getQuestionRecords = async (userId, limitCount)=>{
    try {
        const constraints = [
            where('userId', '==', userId),
            orderBy('lastUpdated', 'desc')
        ];
        if (limitCount) {
            constraints.push(limit(limitCount));
        }
        const q = query(collection(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'question_records'), ...constraints);
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map((doc1)=>({
                id: doc1.id,
                ...doc1.data()
            }));
    } catch (error) {
        console.error('Error getting question records:', error);
        throw error;
    }
};
const subscribeToUserProfile = (userId, callback)=>{
    const docRef = doc(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'user_profiles', userId);
    return onSnapshot(docRef, (doc1)=>{
        if (doc1.exists()) {
            callback(doc1.data());
        } else {
            callback(null);
        }
    });
};
const subscribeToTrainingRecords = (userId, callback)=>{
    const q = query(collection(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'training_records'), where('userId', '==', userId), orderBy('lastUpdated', 'desc'));
    return onSnapshot(q, (querySnapshot)=>{
        const records = querySnapshot.docs.map((doc1)=>({
                id: doc1.id,
                ...doc1.data()
            }));
        callback(records);
    });
};
}),
"[project]/src/lib/auth.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ensureUserProfile": ()=>ensureUserProfile,
    "getRoleDisplayName": ()=>getRoleDisplayName,
    "isMentalTrainer": ()=>isMentalTrainer,
    "isTrainer": ()=>isTrainer,
    "signIn": ()=>signIn,
    "signOutUser": ()=>signOutUser,
    "signUp": ()=>signUp
});
(()=>{
    const e = new Error("Cannot find module 'firebase/auth'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firestore.ts [app-ssr] (ecmascript)");
;
;
;
const signIn = async (email, password)=>{
    try {
        const userCredential = await signInWithEmailAndPassword(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["auth"], email, password);
        return userCredential.user;
    } catch (error) {
        console.error('Error signing in:', error);
        throw error;
    }
};
const signUp = async (email, password, displayName, role = 'TRAINER')=>{
    try {
        const userCredential = await createUserWithEmailAndPassword(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["auth"], email, password);
        const user = userCredential.user;
        // Send email verification
        await sendEmailVerification(user);
        // Create user profile in Firestore
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createUserProfile"])(user.uid, displayName, email, role);
        return user;
    } catch (error) {
        console.error('Error signing up:', error);
        throw error;
    }
};
const signOutUser = async ()=>{
    try {
        await signOut(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["auth"]);
    } catch (error) {
        console.error('Error signing out:', error);
        throw error;
    }
};
const ensureUserProfile = async (user)=>{
    try {
        let userProfile = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUserProfile"])(user.uid);
        if (!userProfile) {
            // Create default profile for existing users
            const displayName = user.displayName || user.email?.split('@')[0] || 'User';
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createUserProfile"])(user.uid, displayName, user.email, 'PLAYER');
            userProfile = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUserProfile"])(user.uid);
        }
        return userProfile;
    } catch (error) {
        console.error('Error ensuring user profile:', error);
        throw error;
    }
};
const isTrainer = (role)=>{
    return role === 'TRAINER' || role === 'MENTAL_TRAINER';
};
const isMentalTrainer = (role)=>{
    return role === 'MENTAL_TRAINER';
};
const getRoleDisplayName = (role)=>{
    switch(role){
        case 'PLAYER':
            return 'Spieler';
        case 'TRAINER':
            return 'Trainer';
        case 'MENTAL_TRAINER':
            return 'Mental-Trainer';
        default:
            return 'Unbekannt';
    }
};
}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AuthProvider": ()=>AuthProvider,
    "useAuth": ()=>useAuth
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module 'firebase/auth'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [userProfile, setUserProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const unsubscribe = onAuthStateChanged(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["auth"], async (user)=>{
            setUser(user);
            if (user) {
                try {
                    // Ensure user profile exists and get it
                    const profile = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ensureUserProfile"])(user);
                    setUserProfile(profile);
                } catch (error) {
                    console.error('Error loading user profile:', error);
                    setUserProfile(null);
                }
            } else {
                setUserProfile(null);
            }
            setLoading(false);
        });
        return ()=>unsubscribe();
    }, []);
    const signOut = async ()=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["auth"].signOut();
            setUser(null);
            setUserProfile(null);
        } catch (error) {
            console.error('Error signing out:', error);
            throw error;
        }
    };
    const value = {
        user,
        userProfile,
        loading,
        signOut
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else //TURBOPACK unreachable
            ;
        } else //TURBOPACK unreachable
        ;
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a3758af8._.js.map