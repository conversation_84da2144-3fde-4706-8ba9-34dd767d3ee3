'use client';

import React from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { MainLayout } from '@/components/layout/MainLayout';

export default function AthleteDashboard() {
  return (
    <ProtectedRoute allowedRoles={['PLAYER']}>
      <MainLayout>
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Athleten Dashboard
            </h1>
            <p className="mt-2 text-gray-600">
              Übersicht über Ihre Trainingsdaten und Trainer-Verbindungen
            </p>
          </div>

          {/* Dashboard content will be implemented in the next task */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Willkommen im Athleten-Portal
            </h2>
            <p className="text-gray-600">
              Hier können Sie Ihre Trainingsdaten einsehen und Ihre Trainer-Verbindungen verwalten.
              Das Dashboard wird in den nächsten Schritten mit detaillierten Funktionen erweitert.
            </p>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
