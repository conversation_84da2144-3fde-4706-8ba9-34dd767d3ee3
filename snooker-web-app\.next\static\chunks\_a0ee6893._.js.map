{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { isTrainer } from '@/lib/auth';\n\nexport default function Home() {\n  const { user, userProfile, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading) {\n      if (user && userProfile) {\n        // Redirect based on user role\n        if (isTrainer(userProfile.role)) {\n          router.push('/trainer/dashboard');\n        } else {\n          router.push('/athlete/dashboard');\n        }\n      } else {\n        // Redirect to login if not authenticated\n        router.push('/login');\n      }\n    }\n  }, [user, userProfile, loading, router]);\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            Die Snooker App\n          </h1>\n          <p className=\"text-gray-600\">Laden...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,QAAQ,aAAa;oBACvB,8BAA8B;oBAC9B,IAAI,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,YAAY,IAAI,GAAG;wBAC/B,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,yCAAyC;oBACzC,OAAO,IAAI,CAAC;gBACd;YACF;QACF;yBAAG;QAAC;QAAM;QAAa;QAAS;KAAO;IAEvC,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,OAAO;AACT;GApCwB;;QACiB,kIAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}