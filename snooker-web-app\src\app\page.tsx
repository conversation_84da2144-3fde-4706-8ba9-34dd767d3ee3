'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { isTrainer } from '@/lib/auth';

export default function Home() {
  const { user, userProfile, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (user && userProfile) {
        // Redirect based on user role
        if (isTrainer(userProfile.role)) {
          router.push('/trainer/dashboard');
        } else {
          router.push('/athlete/dashboard');
        }
      } else {
        // Redirect to login if not authenticated
        router.push('/login');
      }
    }
  }, [user, userProfile, loading, router]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Die Snooker App
          </h1>
          <p className="text-gray-600">Laden...</p>
        </div>
      </div>
    );
  }

  return null;
}
