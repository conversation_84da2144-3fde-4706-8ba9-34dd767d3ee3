'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserConnections } from '@/lib/firestore';
import { UserProfile, UserConnection } from '@/types';
import { Search, User, Calendar, TrendingUp } from 'lucide-react';

interface AthleteListProps {
  onAthleteSelect?: (athlete: UserProfile) => void;
  selectedAthleteId?: string;
}

export const AthleteList: React.FC<AthleteListProps> = ({
  onAthleteSelect,
  selectedAthleteId
}) => {
  const { userProfile } = useAuth();
  const [athletes, setAthletes] = useState<UserProfile[]>([]);
  const [connections, setConnections] = useState<UserConnection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    if (userProfile) {
      loadAthletes();
    }
  }, [userProfile]);

  const loadAthletes = async () => {
    if (!userProfile) return;

    try {
      setLoading(true);
      const userConnections = await getUserConnections(userProfile.userId);
      
      // Filter connections where current user is the trainer
      const trainerConnections = userConnections.filter(
        conn => conn.trainerId === userProfile.userId && conn.status === 'ACCEPTED'
      );
      
      setConnections(trainerConnections);
      
      // For now, we'll create mock athlete profiles based on connections
      // In a real implementation, you would fetch the actual user profiles
      const mockAthletes: UserProfile[] = trainerConnections.map(conn => ({
        userId: conn.playerId,
        displayName: `Athlet ${conn.playerId.slice(-4)}`,
        email: `athlete${conn.playerId.slice(-4)}@example.com`,
        role: 'PLAYER' as const,
        createdAt: new Date(),
        lastLoginAt: new Date(),
        profilePictureUrl: '',
        bio: '',
        location: '',
        dateOfBirth: null,
        phoneNumber: '',
        emergencyContact: '',
        coachingExperience: '',
        specializations: [],
        certifications: [],
        languagesSpoken: ['de'],
        timezone: 'Europe/Berlin',
        notificationPreferences: {
          email: true,
          push: true,
          sms: false
        },
        privacySettings: {
          profileVisibility: 'PUBLIC',
          dataSharing: 'TRAINERS_ONLY'
        }
      }));
      
      setAthletes(mockAthletes);
    } catch (error) {
      console.error('Error loading athletes:', error);
      setError('Fehler beim Laden der Athleten');
    } finally {
      setLoading(false);
    }
  };

  const filteredAthletes = athletes.filter(athlete =>
    athlete.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    athlete.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-red-600">{error}</div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Meine Athleten ({filteredAthletes.length})
        </h2>
        
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Athleten suchen..."
          />
        </div>
      </div>

      <div className="divide-y divide-gray-200">
        {filteredAthletes.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            {searchTerm ? 'Keine Athleten gefunden' : 'Noch keine Athleten verbunden'}
          </div>
        ) : (
          filteredAthletes.map((athlete) => {
            const connection = connections.find(conn => conn.playerId === athlete.userId);
            const isSelected = selectedAthleteId === athlete.userId;
            
            return (
              <div
                key={athlete.userId}
                onClick={() => onAthleteSelect?.(athlete)}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                  isSelected ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                }`}
              >
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                      <User className="h-6 w-6 text-gray-600" />
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {athlete.displayName}
                      </p>
                      <div className="flex items-center space-x-2 text-xs text-gray-500">
                        <Calendar className="h-3 w-3" />
                        <span>
                          Verbunden seit {connection?.createdAt ? 
                            new Date(connection.createdAt).toLocaleDateString('de-DE') : 
                            'Unbekannt'
                          }
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-gray-500 truncate">
                        {athlete.email}
                      </p>
                      <div className="flex items-center space-x-1 text-xs text-green-600">
                        <TrendingUp className="h-3 w-3" />
                        <span>Aktiv</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};
