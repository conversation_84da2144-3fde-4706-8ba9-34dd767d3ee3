{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp, getApps } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\n// Firebase configuration - matches the existing Android app configuration\nconst firebaseConfig = {\n  apiKey: \"AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY\",\n  authDomain: \"die-snooker-app.firebaseapp.com\",\n  projectId: \"die-snooker-app\",\n  storageBucket: \"die-snooker-app.firebasestorage.app\",\n  messagingSenderId: \"547283642216\",\n  appId: \"1:547283642216:web:7f2fdc23dab5ce8430d8dd\",\n  measurementId: \"G-GTFVZZ4LJ\"\n};\n\n// Initialize Firebase only if it hasn't been initialized already\nconst app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\n\nexport default app;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAIA,0EAA0E;AAC1E,MAAM,iBAAiB;IACrB,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,eAAe;IACf,mBAAmB;IACnB,OAAO;IACP,eAAe;AACjB;AAEA,iEAAiE;AACjE,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,cAAc,kBAAkB,SAAS,CAAC,EAAE;AAG1E,MAAM,OAAO,QAAQ;AACrB,MAAM,KAAK,aAAa;uCAEhB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/lib/firestore.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDoc,\n  getDocs,\n  setDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  limit,\n  onSnapshot,\n  Timestamp,\n  serverTimestamp,\n  QueryConstraint\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport {\n  UserProfile,\n  UserConnection,\n  TrainingRecord,\n  ExerciseRecord,\n  ExerciseDefinition,\n  TrainingsplanHistory,\n  QuestionRecord,\n  UserRole\n} from '@/types';\n\n// User Profile operations\nexport const getUserProfile = async (userId: string): Promise<UserProfile | null> => {\n  try {\n    const docRef = doc(db, 'user_profiles', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return docSnap.data() as UserProfile;\n    }\n    return null;\n  } catch (error) {\n    console.error('Error getting user profile:', error);\n    throw error;\n  }\n};\n\nexport const createUserProfile = async (\n  userId: string,\n  displayName: string,\n  email: string,\n  role: UserRole = 'PLAYER'\n): Promise<void> => {\n  try {\n    const userProfile: UserProfile = {\n      userId,\n      displayName,\n      email,\n      role,\n      emailVerified: false,\n      registrationDate: Timestamp.now(),\n      lastUpdated: Timestamp.now()\n    };\n\n    await setDoc(doc(db, 'user_profiles', userId), userProfile);\n  } catch (error) {\n    console.error('Error creating user profile:', error);\n    throw error;\n  }\n};\n\nexport const updateUserProfile = async (\n  userId: string,\n  updates: Partial<UserProfile>\n): Promise<void> => {\n  try {\n    const docRef = doc(db, 'user_profiles', userId);\n    await updateDoc(docRef, {\n      ...updates,\n      lastUpdated: serverTimestamp()\n    });\n  } catch (error) {\n    console.error('Error updating user profile:', error);\n    throw error;\n  }\n};\n\n// User Connections operations\nexport const getUserConnections = async (userId: string): Promise<UserConnection[]> => {\n  try {\n    const q = query(\n      collection(db, 'user_connections'),\n      where('initiatorId', '==', userId)\n    );\n    \n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as UserConnection[];\n  } catch (error) {\n    console.error('Error getting user connections:', error);\n    throw error;\n  }\n};\n\nexport const getTrainerConnections = async (trainerId: string): Promise<UserConnection[]> => {\n  try {\n    const q = query(\n      collection(db, 'user_connections'),\n      where('targetId', '==', trainerId),\n      where('status', '==', 'ACTIVE')\n    );\n    \n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as UserConnection[];\n  } catch (error) {\n    console.error('Error getting trainer connections:', error);\n    throw error;\n  }\n};\n\nexport const createConnection = async (connection: UserConnection): Promise<void> => {\n  try {\n    const connectionId = `${connection.initiatorId}_${connection.targetId}`;\n    await setDoc(doc(db, 'user_connections', connectionId), connection);\n  } catch (error) {\n    console.error('Error creating connection:', error);\n    throw error;\n  }\n};\n\nexport const updateConnection = async (\n  connectionId: string,\n  updates: Partial<UserConnection>\n): Promise<void> => {\n  try {\n    const docRef = doc(db, 'user_connections', connectionId);\n    await updateDoc(docRef, {\n      ...updates,\n      lastUpdated: Date.now()\n    });\n  } catch (error) {\n    console.error('Error updating connection:', error);\n    throw error;\n  }\n};\n\nexport const deleteConnection = async (connectionId: string): Promise<void> => {\n  try {\n    await deleteDoc(doc(db, 'user_connections', connectionId));\n  } catch (error) {\n    console.error('Error deleting connection:', error);\n    throw error;\n  }\n};\n\n// Training Records operations\nexport const getTrainingRecords = async (\n  userId: string,\n  limitCount?: number\n): Promise<TrainingRecord[]> => {\n  try {\n    const constraints: QueryConstraint[] = [\n      where('userId', '==', userId),\n      orderBy('lastUpdated', 'desc')\n    ];\n    \n    if (limitCount) {\n      constraints.push(limit(limitCount));\n    }\n    \n    const q = query(collection(db, 'training_records'), ...constraints);\n    const querySnapshot = await getDocs(q);\n    \n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as TrainingRecord[];\n  } catch (error) {\n    console.error('Error getting training records:', error);\n    throw error;\n  }\n};\n\n// Exercise Records operations\nexport const getExerciseRecords = async (\n  userId: string,\n  limitCount?: number\n): Promise<ExerciseRecord[]> => {\n  try {\n    const constraints: QueryConstraint[] = [\n      where('userId', '==', userId),\n      orderBy('lastUpdated', 'desc')\n    ];\n    \n    if (limitCount) {\n      constraints.push(limit(limitCount));\n    }\n    \n    const q = query(collection(db, 'exercise_records'), ...constraints);\n    const querySnapshot = await getDocs(q);\n    \n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ExerciseRecord[];\n  } catch (error) {\n    console.error('Error getting exercise records:', error);\n    throw error;\n  }\n};\n\n// Exercise Definitions operations\nexport const getExerciseDefinitions = async (userId: string): Promise<ExerciseDefinition[]> => {\n  try {\n    const q = query(\n      collection(db, 'user_exercise_definitions'),\n      where('userId', '==', userId)\n    );\n    \n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ExerciseDefinition[];\n  } catch (error) {\n    console.error('Error getting exercise definitions:', error);\n    throw error;\n  }\n};\n\n// Trainingsplan History operations\nexport const getTrainingsplanHistory = async (\n  userId: string,\n  limitCount?: number\n): Promise<TrainingsplanHistory[]> => {\n  try {\n    const constraints: QueryConstraint[] = [\n      where('userId', '==', userId),\n      orderBy('lastUpdated', 'desc')\n    ];\n    \n    if (limitCount) {\n      constraints.push(limit(limitCount));\n    }\n    \n    const q = query(collection(db, 'trainingsplan_history'), ...constraints);\n    const querySnapshot = await getDocs(q);\n    \n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as TrainingsplanHistory[];\n  } catch (error) {\n    console.error('Error getting trainingsplan history:', error);\n    throw error;\n  }\n};\n\n// Question Records operations\nexport const getQuestionRecords = async (\n  userId: string,\n  limitCount?: number\n): Promise<QuestionRecord[]> => {\n  try {\n    const constraints: QueryConstraint[] = [\n      where('userId', '==', userId),\n      orderBy('lastUpdated', 'desc')\n    ];\n    \n    if (limitCount) {\n      constraints.push(limit(limitCount));\n    }\n    \n    const q = query(collection(db, 'question_records'), ...constraints);\n    const querySnapshot = await getDocs(q);\n    \n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as QuestionRecord[];\n  } catch (error) {\n    console.error('Error getting question records:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToUserProfile = (\n  userId: string,\n  callback: (profile: UserProfile | null) => void\n) => {\n  const docRef = doc(db, 'user_profiles', userId);\n  return onSnapshot(docRef, (doc) => {\n    if (doc.exists()) {\n      callback(doc.data() as UserProfile);\n    } else {\n      callback(null);\n    }\n  });\n};\n\nexport const subscribeToTrainingRecords = (\n  userId: string,\n  callback: (records: TrainingRecord[]) => void\n) => {\n  const q = query(\n    collection(db, 'training_records'),\n    where('userId', '==', userId),\n    orderBy('lastUpdated', 'desc')\n  );\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const records = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as TrainingRecord[];\n    callback(records);\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAiBA;;;AAaO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,SAAS,IAAI,sHAAA,CAAA,KAAE,EAAE,iBAAiB;QACxC,MAAM,UAAU,MAAM,OAAO;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO,QAAQ,IAAI;QACrB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB,OAC/B,QACA,aACA,OACA,OAAiB,QAAQ;IAEzB,IAAI;QACF,MAAM,cAA2B;YAC/B;YACA;YACA;YACA;YACA,eAAe;YACf,kBAAkB,UAAU,GAAG;YAC/B,aAAa,UAAU,GAAG;QAC5B;QAEA,MAAM,OAAO,IAAI,sHAAA,CAAA,KAAE,EAAE,iBAAiB,SAAS;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB,OAC/B,QACA;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,sHAAA,CAAA,KAAE,EAAE,iBAAiB;QACxC,MAAM,UAAU,QAAQ;YACtB,GAAG,OAAO;YACV,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,IAAI,MACR,WAAW,sHAAA,CAAA,KAAE,EAAE,qBACf,MAAM,eAAe,MAAM;QAG7B,MAAM,gBAAgB,MAAM,QAAQ;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OAAO;IAC1C,IAAI;QACF,MAAM,IAAI,MACR,WAAW,sHAAA,CAAA,KAAE,EAAE,qBACf,MAAM,YAAY,MAAM,YACxB,MAAM,UAAU,MAAM;QAGxB,MAAM,gBAAgB,MAAM,QAAQ;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,eAAe,GAAG,WAAW,WAAW,CAAC,CAAC,EAAE,WAAW,QAAQ,EAAE;QACvE,MAAM,OAAO,IAAI,sHAAA,CAAA,KAAE,EAAE,oBAAoB,eAAe;IAC1D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAC9B,cACA;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,sHAAA,CAAA,KAAE,EAAE,oBAAoB;QAC3C,MAAM,UAAU,QAAQ;YACtB,GAAG,OAAO;YACV,aAAa,KAAK,GAAG;QACvB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,UAAU,IAAI,sHAAA,CAAA,KAAE,EAAE,oBAAoB;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,OAChC,QACA;IAEA,IAAI;QACF,MAAM,cAAiC;YACrC,MAAM,UAAU,MAAM;YACtB,QAAQ,eAAe;SACxB;QAED,IAAI,YAAY;YACd,YAAY,IAAI,CAAC,MAAM;QACzB;QAEA,MAAM,IAAI,MAAM,WAAW,sHAAA,CAAA,KAAE,EAAE,wBAAwB;QACvD,MAAM,gBAAgB,MAAM,QAAQ;QAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,OAChC,QACA;IAEA,IAAI;QACF,MAAM,cAAiC;YACrC,MAAM,UAAU,MAAM;YACtB,QAAQ,eAAe;SACxB;QAED,IAAI,YAAY;YACd,YAAY,IAAI,CAAC,MAAM;QACzB;QAEA,MAAM,IAAI,MAAM,WAAW,sHAAA,CAAA,KAAE,EAAE,wBAAwB;QACvD,MAAM,gBAAgB,MAAM,QAAQ;QAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,yBAAyB,OAAO;IAC3C,IAAI;QACF,MAAM,IAAI,MACR,WAAW,sHAAA,CAAA,KAAE,EAAE,8BACf,MAAM,UAAU,MAAM;QAGxB,MAAM,gBAAgB,MAAM,QAAQ;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;AAGO,MAAM,0BAA0B,OACrC,QACA;IAEA,IAAI;QACF,MAAM,cAAiC;YACrC,MAAM,UAAU,MAAM;YACtB,QAAQ,eAAe;SACxB;QAED,IAAI,YAAY;YACd,YAAY,IAAI,CAAC,MAAM;QACzB;QAEA,MAAM,IAAI,MAAM,WAAW,sHAAA,CAAA,KAAE,EAAE,6BAA6B;QAC5D,MAAM,gBAAgB,MAAM,QAAQ;QAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,OAChC,QACA;IAEA,IAAI;QACF,MAAM,cAAiC;YACrC,MAAM,UAAU,MAAM;YACtB,QAAQ,eAAe;SACxB;QAED,IAAI,YAAY;YACd,YAAY,IAAI,CAAC,MAAM;QACzB;QAEA,MAAM,IAAI,MAAM,WAAW,sHAAA,CAAA,KAAE,EAAE,wBAAwB;QACvD,MAAM,gBAAgB,MAAM,QAAQ;QAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,yBAAyB,CACpC,QACA;IAEA,MAAM,SAAS,IAAI,sHAAA,CAAA,KAAE,EAAE,iBAAiB;IACxC,OAAO,WAAW,QAAQ,CAAC;QACzB,IAAI,KAAI,MAAM,IAAI;YAChB,SAAS,KAAI,IAAI;QACnB,OAAO;YACL,SAAS;QACX;IACF;AACF;AAEO,MAAM,6BAA6B,CACxC,QACA;IAEA,MAAM,IAAI,MACR,WAAW,sHAAA,CAAA,KAAE,EAAE,qBACf,MAAM,UAAU,MAAM,SACtB,QAAQ,eAAe;IAGzB,OAAO,WAAW,GAAG,CAAC;QACpB,MAAM,UAAU,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBAC7C,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/lib/auth.ts"], "sourcesContent": ["import {\n  signInWithEmailAndPassword,\n  createUserWithEmailAndPassword,\n  signOut,\n  sendEmailVerification,\n  User\n} from 'firebase/auth';\nimport { auth } from './firebase';\nimport { createUserProfile, getUserProfile } from './firestore';\nimport { UserRole } from '@/types';\n\n// Sign in with email and password\nexport const signIn = async (email: string, password: string): Promise<User> => {\n  try {\n    const userCredential = await signInWithEmailAndPassword(auth, email, password);\n    return userCredential.user;\n  } catch (error) {\n    console.error('Error signing in:', error);\n    throw error;\n  }\n};\n\n// Create new user account (for trainers)\nexport const signUp = async (\n  email: string,\n  password: string,\n  displayName: string,\n  role: UserRole = 'TRAINER'\n): Promise<User> => {\n  try {\n    const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n    const user = userCredential.user;\n    \n    // Send email verification\n    await sendEmailVerification(user);\n    \n    // Create user profile in Firestore\n    await createUserProfile(user.uid, displayName, email, role);\n    \n    return user;\n  } catch (error) {\n    console.error('Error signing up:', error);\n    throw error;\n  }\n};\n\n// Sign out\nexport const signOutUser = async (): Promise<void> => {\n  try {\n    await signOut(auth);\n  } catch (error) {\n    console.error('Error signing out:', error);\n    throw error;\n  }\n};\n\n// Ensure user profile exists (for existing users from Android app)\nexport const ensureUserProfile = async (user: User) => {\n  try {\n    let userProfile = await getUserProfile(user.uid);\n    \n    if (!userProfile) {\n      // Create default profile for existing users\n      const displayName = user.displayName || user.email?.split('@')[0] || 'User';\n      await createUserProfile(user.uid, displayName, user.email!, 'PLAYER');\n      userProfile = await getUserProfile(user.uid);\n    }\n    \n    return userProfile;\n  } catch (error) {\n    console.error('Error ensuring user profile:', error);\n    throw error;\n  }\n};\n\n// Check if user has trainer role\nexport const isTrainer = (role: UserRole): boolean => {\n  return role === 'TRAINER' || role === 'MENTAL_TRAINER';\n};\n\n// Check if user has mental trainer role\nexport const isMentalTrainer = (role: UserRole): boolean => {\n  return role === 'MENTAL_TRAINER';\n};\n\n// Get user role display name in German\nexport const getRoleDisplayName = (role: UserRole): string => {\n  switch (role) {\n    case 'PLAYER':\n      return 'Spieler';\n    case 'TRAINER':\n      return 'Trainer';\n    case 'MENTAL_TRAINER':\n      return 'Mental-Trainer';\n    default:\n      return 'Unbekannt';\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAOA;AACA;;;;AAIO,MAAM,SAAS,OAAO,OAAe;IAC1C,IAAI;QACF,MAAM,iBAAiB,MAAM,2BAA2B,sHAAA,CAAA,OAAI,EAAE,OAAO;QACrE,OAAO,eAAe,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM;IACR;AACF;AAGO,MAAM,SAAS,OACpB,OACA,UACA,aACA,OAAiB,SAAS;IAE1B,IAAI;QACF,MAAM,iBAAiB,MAAM,+BAA+B,sHAAA,CAAA,OAAI,EAAE,OAAO;QACzE,MAAM,OAAO,eAAe,IAAI;QAEhC,0BAA0B;QAC1B,MAAM,sBAAsB;QAE5B,mCAAmC;QACnC,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,GAAG,EAAE,aAAa,OAAO;QAEtD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,QAAQ,sHAAA,CAAA,OAAI;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,IAAI,cAAc,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG;QAE/C,IAAI,CAAC,aAAa;YAChB,4CAA4C;YAC5C,MAAM,cAAc,KAAK,WAAW,IAAI,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;YACrE,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,GAAG,EAAE,aAAa,KAAK,KAAK,EAAG;YAC5D,cAAc,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG;QAC7C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,SAAS,aAAa,SAAS;AACxC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,SAAS;AAClB;AAGO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, onAuthStateChanged } from 'firebase/auth';\nimport { auth } from '@/lib/firebase';\nimport { getUserProfile } from '@/lib/firestore';\nimport { ensureUserProfile } from '@/lib/auth';\nimport { UserProfile } from '@/types';\n\ninterface AuthContextType {\n  user: User | null;\n  userProfile: UserProfile | null;\n  loading: boolean;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      setUser(user);\n      \n      if (user) {\n        try {\n          // Ensure user profile exists and get it\n          const profile = await ensureUserProfile(user);\n          setUserProfile(profile);\n        } catch (error) {\n          console.error('Error loading user profile:', error);\n          setUserProfile(null);\n        }\n      } else {\n        setUserProfile(null);\n      }\n      \n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const signOut = async () => {\n    try {\n      await auth.signOut();\n      setUser(null);\n      setUserProfile(null);\n    } catch (error) {\n      console.error('Error signing out:', error);\n      throw error;\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    userProfile,\n    loading,\n    signOut\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;AAEA;AAEA;AANA;;;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,mBAAmB,sHAAA,CAAA,OAAI,EAAE,OAAO;YAClD,QAAQ;YAER,IAAI,MAAM;gBACR,IAAI;oBACF,wCAAwC;oBACxC,MAAM,UAAU,MAAM,CAAA,GAAA,kHAAA,CAAA,oBAAiB,AAAD,EAAE;oBACxC,eAAe;gBACjB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,eAAe;gBACjB;YACF,OAAO;gBACL,eAAe;YACjB;YAEA,WAAW;QACb;QAEA,OAAO,IAAM;IACf,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,MAAM,sHAAA,CAAA,OAAI,CAAC,OAAO;YAClB,QAAQ;YACR,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}