{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp, getApps } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\n// Firebase configuration - matches the existing Android app configuration\nconst firebaseConfig = {\n  apiKey: \"AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY\",\n  authDomain: \"die-snooker-app.firebaseapp.com\",\n  projectId: \"die-snooker-app\",\n  storageBucket: \"die-snooker-app.firebasestorage.app\",\n  messagingSenderId: \"547283642216\",\n  appId: \"1:547283642216:web:7f2fdc23dab5ce8430d8dd\",\n  measurementId: \"G-GTFVZZ4LJ\"\n};\n\n// Initialize Firebase only if it hasn't been initialized already\nconst app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\n\nexport default app;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAIA,0EAA0E;AAC1E,MAAM,iBAAiB;IACrB,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,eAAe;IACf,mBAAmB;IACnB,OAAO;IACP,eAAe;AACjB;AAEA,iEAAiE;AACjE,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,cAAc,kBAAkB,SAAS,CAAC,EAAE;AAG1E,MAAM,OAAO,QAAQ;AACrB,MAAM,KAAK,aAAa;uCAEhB", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/lib/firestore.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDoc,\n  getDocs,\n  setDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  limit,\n  onSnapshot,\n  Timestamp,\n  serverTimestamp,\n  QueryConstraint\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport {\n  UserProfile,\n  UserConnection,\n  TrainingRecord,\n  ExerciseRecord,\n  ExerciseDefinition,\n  TrainingsplanHistory,\n  QuestionRecord,\n  UserRole\n} from '@/types';\n\n// User Profile operations\nexport const getUserProfile = async (userId: string): Promise<UserProfile | null> => {\n  try {\n    const docRef = doc(db, 'user_profiles', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return docSnap.data() as UserProfile;\n    }\n    return null;\n  } catch (error) {\n    console.error('Error getting user profile:', error);\n    throw error;\n  }\n};\n\nexport const createUserProfile = async (\n  userId: string,\n  displayName: string,\n  email: string,\n  role: UserRole = 'PLAYER'\n): Promise<void> => {\n  try {\n    const userProfile: UserProfile = {\n      userId,\n      displayName,\n      email,\n      role,\n      emailVerified: false,\n      registrationDate: Timestamp.now(),\n      lastUpdated: Timestamp.now()\n    };\n\n    await setDoc(doc(db, 'user_profiles', userId), userProfile);\n  } catch (error) {\n    console.error('Error creating user profile:', error);\n    throw error;\n  }\n};\n\nexport const updateUserProfile = async (\n  userId: string,\n  updates: Partial<UserProfile>\n): Promise<void> => {\n  try {\n    const docRef = doc(db, 'user_profiles', userId);\n    await updateDoc(docRef, {\n      ...updates,\n      lastUpdated: serverTimestamp()\n    });\n  } catch (error) {\n    console.error('Error updating user profile:', error);\n    throw error;\n  }\n};\n\n// User Connections operations\nexport const getUserConnections = async (userId: string): Promise<UserConnection[]> => {\n  try {\n    const q = query(\n      collection(db, 'user_connections'),\n      where('initiatorId', '==', userId)\n    );\n    \n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as UserConnection[];\n  } catch (error) {\n    console.error('Error getting user connections:', error);\n    throw error;\n  }\n};\n\nexport const getTrainerConnections = async (trainerId: string): Promise<UserConnection[]> => {\n  try {\n    const q = query(\n      collection(db, 'user_connections'),\n      where('targetId', '==', trainerId),\n      where('status', '==', 'ACTIVE')\n    );\n    \n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as UserConnection[];\n  } catch (error) {\n    console.error('Error getting trainer connections:', error);\n    throw error;\n  }\n};\n\nexport const createConnection = async (connection: UserConnection): Promise<void> => {\n  try {\n    const connectionId = `${connection.initiatorId}_${connection.targetId}`;\n    await setDoc(doc(db, 'user_connections', connectionId), connection);\n  } catch (error) {\n    console.error('Error creating connection:', error);\n    throw error;\n  }\n};\n\nexport const updateConnection = async (\n  connectionId: string,\n  updates: Partial<UserConnection>\n): Promise<void> => {\n  try {\n    const docRef = doc(db, 'user_connections', connectionId);\n    await updateDoc(docRef, {\n      ...updates,\n      lastUpdated: Date.now()\n    });\n  } catch (error) {\n    console.error('Error updating connection:', error);\n    throw error;\n  }\n};\n\nexport const deleteConnection = async (connectionId: string): Promise<void> => {\n  try {\n    await deleteDoc(doc(db, 'user_connections', connectionId));\n  } catch (error) {\n    console.error('Error deleting connection:', error);\n    throw error;\n  }\n};\n\n// Training Records operations\nexport const getTrainingRecords = async (\n  userId: string,\n  limitCount?: number\n): Promise<TrainingRecord[]> => {\n  try {\n    const constraints: QueryConstraint[] = [\n      where('userId', '==', userId),\n      orderBy('lastUpdated', 'desc')\n    ];\n    \n    if (limitCount) {\n      constraints.push(limit(limitCount));\n    }\n    \n    const q = query(collection(db, 'training_records'), ...constraints);\n    const querySnapshot = await getDocs(q);\n    \n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as TrainingRecord[];\n  } catch (error) {\n    console.error('Error getting training records:', error);\n    throw error;\n  }\n};\n\n// Exercise Records operations\nexport const getExerciseRecords = async (\n  userId: string,\n  limitCount?: number\n): Promise<ExerciseRecord[]> => {\n  try {\n    const constraints: QueryConstraint[] = [\n      where('userId', '==', userId),\n      orderBy('lastUpdated', 'desc')\n    ];\n    \n    if (limitCount) {\n      constraints.push(limit(limitCount));\n    }\n    \n    const q = query(collection(db, 'exercise_records'), ...constraints);\n    const querySnapshot = await getDocs(q);\n    \n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ExerciseRecord[];\n  } catch (error) {\n    console.error('Error getting exercise records:', error);\n    throw error;\n  }\n};\n\n// Exercise Definitions operations\nexport const getExerciseDefinitions = async (userId: string): Promise<ExerciseDefinition[]> => {\n  try {\n    const q = query(\n      collection(db, 'user_exercise_definitions'),\n      where('userId', '==', userId)\n    );\n    \n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ExerciseDefinition[];\n  } catch (error) {\n    console.error('Error getting exercise definitions:', error);\n    throw error;\n  }\n};\n\n// Trainingsplan History operations\nexport const getTrainingsplanHistory = async (\n  userId: string,\n  limitCount?: number\n): Promise<TrainingsplanHistory[]> => {\n  try {\n    const constraints: QueryConstraint[] = [\n      where('userId', '==', userId),\n      orderBy('lastUpdated', 'desc')\n    ];\n    \n    if (limitCount) {\n      constraints.push(limit(limitCount));\n    }\n    \n    const q = query(collection(db, 'trainingsplan_history'), ...constraints);\n    const querySnapshot = await getDocs(q);\n    \n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as TrainingsplanHistory[];\n  } catch (error) {\n    console.error('Error getting trainingsplan history:', error);\n    throw error;\n  }\n};\n\n// Question Records operations\nexport const getQuestionRecords = async (\n  userId: string,\n  limitCount?: number\n): Promise<QuestionRecord[]> => {\n  try {\n    const constraints: QueryConstraint[] = [\n      where('userId', '==', userId),\n      orderBy('lastUpdated', 'desc')\n    ];\n    \n    if (limitCount) {\n      constraints.push(limit(limitCount));\n    }\n    \n    const q = query(collection(db, 'question_records'), ...constraints);\n    const querySnapshot = await getDocs(q);\n    \n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as QuestionRecord[];\n  } catch (error) {\n    console.error('Error getting question records:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToUserProfile = (\n  userId: string,\n  callback: (profile: UserProfile | null) => void\n) => {\n  const docRef = doc(db, 'user_profiles', userId);\n  return onSnapshot(docRef, (doc) => {\n    if (doc.exists()) {\n      callback(doc.data() as UserProfile);\n    } else {\n      callback(null);\n    }\n  });\n};\n\nexport const subscribeToTrainingRecords = (\n  userId: string,\n  callback: (records: TrainingRecord[]) => void\n) => {\n  const q = query(\n    collection(db, 'training_records'),\n    where('userId', '==', userId),\n    orderBy('lastUpdated', 'desc')\n  );\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const records = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as TrainingRecord[];\n    callback(records);\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAiBA;;;AAaO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,SAAS,IAAI,yHAAA,CAAA,KAAE,EAAE,iBAAiB;QACxC,MAAM,UAAU,MAAM,OAAO;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO,QAAQ,IAAI;QACrB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB,eAC/B,QACA,aACA;QACA,wEAAiB;IAEjB,IAAI;QACF,MAAM,cAA2B;YAC/B;YACA;YACA;YACA;YACA,eAAe;YACf,kBAAkB,UAAU,GAAG;YAC/B,aAAa,UAAU,GAAG;QAC5B;QAEA,MAAM,OAAO,IAAI,yHAAA,CAAA,KAAE,EAAE,iBAAiB,SAAS;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB,OAC/B,QACA;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,yHAAA,CAAA,KAAE,EAAE,iBAAiB;QACxC,MAAM,UAAU,QAAQ;YACtB,GAAG,OAAO;YACV,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,IAAI,MACR,WAAW,yHAAA,CAAA,KAAE,EAAE,qBACf,MAAM,eAAe,MAAM;QAG7B,MAAM,gBAAgB,MAAM,QAAQ;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OAAO;IAC1C,IAAI;QACF,MAAM,IAAI,MACR,WAAW,yHAAA,CAAA,KAAE,EAAE,qBACf,MAAM,YAAY,MAAM,YACxB,MAAM,UAAU,MAAM;QAGxB,MAAM,gBAAgB,MAAM,QAAQ;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,eAAe,AAAC,GAA4B,OAA1B,WAAW,WAAW,EAAC,KAAuB,OAApB,WAAW,QAAQ;QACrE,MAAM,OAAO,IAAI,yHAAA,CAAA,KAAE,EAAE,oBAAoB,eAAe;IAC1D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAC9B,cACA;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,yHAAA,CAAA,KAAE,EAAE,oBAAoB;QAC3C,MAAM,UAAU,QAAQ;YACtB,GAAG,OAAO;YACV,aAAa,KAAK,GAAG;QACvB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,UAAU,IAAI,yHAAA,CAAA,KAAE,EAAE,oBAAoB;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,OAChC,QACA;IAEA,IAAI;QACF,MAAM,cAAiC;YACrC,MAAM,UAAU,MAAM;YACtB,QAAQ,eAAe;SACxB;QAED,IAAI,YAAY;YACd,YAAY,IAAI,CAAC,MAAM;QACzB;QAEA,MAAM,IAAI,MAAM,WAAW,yHAAA,CAAA,KAAE,EAAE,wBAAwB;QACvD,MAAM,gBAAgB,MAAM,QAAQ;QAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,OAChC,QACA;IAEA,IAAI;QACF,MAAM,cAAiC;YACrC,MAAM,UAAU,MAAM;YACtB,QAAQ,eAAe;SACxB;QAED,IAAI,YAAY;YACd,YAAY,IAAI,CAAC,MAAM;QACzB;QAEA,MAAM,IAAI,MAAM,WAAW,yHAAA,CAAA,KAAE,EAAE,wBAAwB;QACvD,MAAM,gBAAgB,MAAM,QAAQ;QAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,yBAAyB,OAAO;IAC3C,IAAI;QACF,MAAM,IAAI,MACR,WAAW,yHAAA,CAAA,KAAE,EAAE,8BACf,MAAM,UAAU,MAAM;QAGxB,MAAM,gBAAgB,MAAM,QAAQ;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;AAGO,MAAM,0BAA0B,OACrC,QACA;IAEA,IAAI;QACF,MAAM,cAAiC;YACrC,MAAM,UAAU,MAAM;YACtB,QAAQ,eAAe;SACxB;QAED,IAAI,YAAY;YACd,YAAY,IAAI,CAAC,MAAM;QACzB;QAEA,MAAM,IAAI,MAAM,WAAW,yHAAA,CAAA,KAAE,EAAE,6BAA6B;QAC5D,MAAM,gBAAgB,MAAM,QAAQ;QAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,OAChC,QACA;IAEA,IAAI;QACF,MAAM,cAAiC;YACrC,MAAM,UAAU,MAAM;YACtB,QAAQ,eAAe;SACxB;QAED,IAAI,YAAY;YACd,YAAY,IAAI,CAAC,MAAM;QACzB;QAEA,MAAM,IAAI,MAAM,WAAW,yHAAA,CAAA,KAAE,EAAE,wBAAwB;QACvD,MAAM,gBAAgB,MAAM,QAAQ;QAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBACpC,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,yBAAyB,CACpC,QACA;IAEA,MAAM,SAAS,IAAI,yHAAA,CAAA,KAAE,EAAE,iBAAiB;IACxC,OAAO,WAAW,QAAQ,CAAC;QACzB,IAAI,KAAI,MAAM,IAAI;YAChB,SAAS,KAAI,IAAI;QACnB,OAAO;YACL,SAAS;QACX;IACF;AACF;AAEO,MAAM,6BAA6B,CACxC,QACA;IAEA,MAAM,IAAI,MACR,WAAW,yHAAA,CAAA,KAAE,EAAE,qBACf,MAAM,UAAU,MAAM,SACtB,QAAQ,eAAe;IAGzB,OAAO,WAAW,GAAG,CAAC;QACpB,MAAM,UAAU,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,OAAO,CAAC;gBAC7C,IAAI,KAAI,EAAE;gBACV,GAAG,KAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/lib/auth.ts"], "sourcesContent": ["import {\n  signInWithEmailAndPassword,\n  createUserWithEmailAndPassword,\n  signOut,\n  sendEmailVerification,\n  User\n} from 'firebase/auth';\nimport { auth } from './firebase';\nimport { createUserProfile, getUserProfile } from './firestore';\nimport { UserRole } from '@/types';\n\n// Sign in with email and password\nexport const signIn = async (email: string, password: string): Promise<User> => {\n  try {\n    const userCredential = await signInWithEmailAndPassword(auth, email, password);\n    return userCredential.user;\n  } catch (error) {\n    console.error('Error signing in:', error);\n    throw error;\n  }\n};\n\n// Create new user account (for trainers)\nexport const signUp = async (\n  email: string,\n  password: string,\n  displayName: string,\n  role: UserRole = 'TRAINER'\n): Promise<User> => {\n  try {\n    const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n    const user = userCredential.user;\n    \n    // Send email verification\n    await sendEmailVerification(user);\n    \n    // Create user profile in Firestore\n    await createUserProfile(user.uid, displayName, email, role);\n    \n    return user;\n  } catch (error) {\n    console.error('Error signing up:', error);\n    throw error;\n  }\n};\n\n// Sign out\nexport const signOutUser = async (): Promise<void> => {\n  try {\n    await signOut(auth);\n  } catch (error) {\n    console.error('Error signing out:', error);\n    throw error;\n  }\n};\n\n// Ensure user profile exists (for existing users from Android app)\nexport const ensureUserProfile = async (user: User) => {\n  try {\n    let userProfile = await getUserProfile(user.uid);\n    \n    if (!userProfile) {\n      // Create default profile for existing users\n      const displayName = user.displayName || user.email?.split('@')[0] || 'User';\n      await createUserProfile(user.uid, displayName, user.email!, 'PLAYER');\n      userProfile = await getUserProfile(user.uid);\n    }\n    \n    return userProfile;\n  } catch (error) {\n    console.error('Error ensuring user profile:', error);\n    throw error;\n  }\n};\n\n// Check if user has trainer role\nexport const isTrainer = (role: UserRole): boolean => {\n  return role === 'TRAINER' || role === 'MENTAL_TRAINER';\n};\n\n// Check if user has mental trainer role\nexport const isMentalTrainer = (role: UserRole): boolean => {\n  return role === 'MENTAL_TRAINER';\n};\n\n// Get user role display name in German\nexport const getRoleDisplayName = (role: UserRole): string => {\n  switch (role) {\n    case 'PLAYER':\n      return 'Spieler';\n    case 'TRAINER':\n      return 'Trainer';\n    case 'MENTAL_TRAINER':\n      return 'Mental-Trainer';\n    default:\n      return 'Unbekannt';\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAOA;AACA;;;;AAIO,MAAM,SAAS,OAAO,OAAe;IAC1C,IAAI;QACF,MAAM,iBAAiB,MAAM,2BAA2B,yHAAA,CAAA,OAAI,EAAE,OAAO;QACrE,OAAO,eAAe,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM;IACR;AACF;AAGO,MAAM,SAAS,eACpB,OACA,UACA;QACA,wEAAiB;IAEjB,IAAI;QACF,MAAM,iBAAiB,MAAM,+BAA+B,yHAAA,CAAA,OAAI,EAAE,OAAO;QACzE,MAAM,OAAO,eAAe,IAAI;QAEhC,0BAA0B;QAC1B,MAAM,sBAAsB;QAE5B,mCAAmC;QACnC,MAAM,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,GAAG,EAAE,aAAa,OAAO;QAEtD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,QAAQ,yHAAA,CAAA,OAAI;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,IAAI,cAAc,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG;QAE/C,IAAI,CAAC,aAAa;gBAEwB;YADxC,4CAA4C;YAC5C,MAAM,cAAc,KAAK,WAAW,MAAI,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,KAAI;YACrE,MAAM,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,GAAG,EAAE,aAAa,KAAK,KAAK,EAAG;YAC5D,cAAc,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG;QAC7C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,SAAS,aAAa,SAAS;AACxC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,SAAS;AAClB;AAGO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, onAuthStateChanged } from 'firebase/auth';\nimport { auth } from '@/lib/firebase';\nimport { getUserProfile } from '@/lib/firestore';\nimport { ensureUserProfile } from '@/lib/auth';\nimport { UserProfile } from '@/types';\n\ninterface AuthContextType {\n  user: User | null;\n  userProfile: UserProfile | null;\n  loading: boolean;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      setUser(user);\n      \n      if (user) {\n        try {\n          // Ensure user profile exists and get it\n          const profile = await ensureUserProfile(user);\n          setUserProfile(profile);\n        } catch (error) {\n          console.error('Error loading user profile:', error);\n          setUserProfile(null);\n        }\n      } else {\n        setUserProfile(null);\n      }\n      \n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const signOut = async () => {\n    try {\n      await auth.signOut();\n      setUser(null);\n      setUserProfile(null);\n    } catch (error) {\n      console.error('Error signing out:', error);\n      throw error;\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    userProfile,\n    loading,\n    signOut\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;AAEA;AAEA;;;AANA;;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,eAA4C;QAAC,EAAE,QAAQ,EAAE;;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,cAAc,mBAAmB,yHAAA,CAAA,OAAI;sDAAE,OAAO;oBAClD,QAAQ;oBAER,IAAI,MAAM;wBACR,IAAI;4BACF,wCAAwC;4BACxC,MAAM,UAAU,MAAM,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE;4BACxC,eAAe;wBACjB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,+BAA+B;4BAC7C,eAAe;wBACjB;oBACF,OAAO;wBACL,eAAe;oBACjB;oBAEA,WAAW;gBACb;;YAEA;0CAAO,IAAM;;QACf;iCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,MAAM,yHAAA,CAAA,OAAI,CAAC,OAAO;YAClB,QAAQ;YACR,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAnDa;KAAA", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Home/Dokumente/AndroidStudioProjects/DieSnookerApp%20%2B%20web%20page/snooker-web-app/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}