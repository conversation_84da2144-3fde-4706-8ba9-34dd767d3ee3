'use client';

import React from 'react';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  <PERSON><PERSON><PERSON>,
  <PERSON>
} from 'chart.js';
import { Radar } from 'react-chartjs-2';

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

interface SelfAssessmentChartProps {
  athleteId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  height?: number;
}

export const SelfAssessmentChart: React.FC<SelfAssessmentChartProps> = ({
  athleteId,
  dateRange,
  height = 400
}) => {
  // Self-assessment categories
  const assessmentCategories = [
    'Technik',
    'Mentale Stärke',
    'Körperliche Fitness',
    'Taktisches Verständnis',
    'Konzentration',
    'Selbstvertrauen',
    'Stressresistenz',
    'Motivation'
  ];

  // Mock data for current and previous assessments
  const mockData = {
    current: [8.2, 7.5, 6.8, 7.9, 8.1, 7.3, 6.9, 8.5],
    previous: [7.8, 7.1, 6.5, 7.5, 7.8, 7.0, 6.6, 8.2],
    target: [9.0, 8.5, 8.0, 8.5, 9.0, 8.0, 8.0, 9.0]
  };

  const data = {
    labels: assessmentCategories,
    datasets: [
      {
        label: 'Aktuelle Bewertung',
        data: mockData.current,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      },
      {
        label: 'Vorherige Bewertung',
        data: mockData.previous,
        borderColor: 'rgb(156, 163, 175)',
        backgroundColor: 'rgba(156, 163, 175, 0.1)',
        borderWidth: 2,
        borderDash: [5, 5],
        pointBackgroundColor: 'rgb(156, 163, 175)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 3,
        pointHoverRadius: 5
      },
      {
        label: 'Zielwerte',
        data: mockData.target,
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        borderWidth: 2,
        borderDash: [10, 5],
        pointBackgroundColor: 'rgb(34, 197, 94)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 3,
        pointHoverRadius: 5
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: athleteId ? `Selbsteinschätzung - Athlet ${athleteId.slice(-4)}` : 'Selbsteinschätzung - Durchschnitt',
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          bottom: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || '';
            const value = context.parsed.r;
            return `${label}: ${value.toFixed(1)}/10`;
          }
        }
      }
    },
    scales: {
      r: {
        beginAtZero: true,
        min: 0,
        max: 10,
        ticks: {
          stepSize: 2,
          callback: function(value: any) {
            return value.toString();
          },
          font: {
            size: 10
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        angleLines: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        pointLabels: {
          font: {
            size: 11,
            weight: 'bold'
          },
          color: '#374151'
        }
      }
    },
    elements: {
      point: {
        hoverBorderWidth: 3
      }
    }
  };

  // Calculate improvements
  const improvements = mockData.current.map((current, index) => {
    const previous = mockData.previous[index];
    return ((current - previous) / previous * 100).toFixed(1);
  });

  const averageCurrent = (mockData.current.reduce((sum, val) => sum + val, 0) / mockData.current.length).toFixed(1);
  const averagePrevious = (mockData.previous.reduce((sum, val) => sum + val, 0) / mockData.previous.length).toFixed(1);
  const overallImprovement = (((parseFloat(averageCurrent) - parseFloat(averagePrevious)) / parseFloat(averagePrevious)) * 100).toFixed(1);

  const bestCategory = assessmentCategories[mockData.current.indexOf(Math.max(...mockData.current))];
  const improvementNeeded = assessmentCategories[mockData.current.indexOf(Math.min(...mockData.current))];

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div style={{ height: `${height}px` }}>
        <Radar data={data} options={options} />
      </div>
      
      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {averageCurrent}
          </div>
          <div className="text-sm text-gray-600">Aktueller Durchschnitt</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            +{overallImprovement}%
          </div>
          <div className="text-sm text-gray-600">Gesamtverbesserung</div>
        </div>
        <div className="text-center p-3 bg-yellow-50 rounded-lg">
          <div className="text-lg font-bold text-yellow-600">
            {bestCategory}
          </div>
          <div className="text-sm text-gray-600">Stärkste Kategorie</div>
        </div>
        <div className="text-center p-3 bg-red-50 rounded-lg">
          <div className="text-lg font-bold text-red-600">
            {improvementNeeded}
          </div>
          <div className="text-sm text-gray-600">Verbesserungsbedarf</div>
        </div>
      </div>

      {/* Detailed improvements */}
      <div className="mt-4 bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-gray-700 mb-2">Verbesserungen im Detail:</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
          {assessmentCategories.map((category, index) => (
            <div key={category} className="flex justify-between">
              <span className="text-gray-600">{category}:</span>
              <span className={`font-medium ${
                parseFloat(improvements[index]) >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {parseFloat(improvements[index]) >= 0 ? '+' : ''}{improvements[index]}%
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
