'use client';

import React, { useState } from 'react';
import { TrainingProgressChart } from './charts/TrainingProgressChart';
import { ExerciseCompletionChart } from './charts/ExerciseCompletionChart';
import { SelfAssessmentChart } from './charts/SelfAssessmentChart';
import { UserProfile } from '@/types';
import { 
  TrendingUp, 
  BarChart3, 
  Target, 
  Calendar,
  Download,
  Maximize2,
  Minimize2
} from 'lucide-react';

interface DataVisualizationProps {
  selectedAthlete?: UserProfile | null;
  dateRange?: {
    start: string;
    end: string;
  };
}

type ChartType = 'progress' | 'exercises' | 'assessment' | 'all';

export const DataVisualization: React.FC<DataVisualizationProps> = ({
  selectedAthlete,
  dateRange
}) => {
  const [activeChart, setActiveChart] = useState<ChartType>('all');
  const [isFullscreen, setIsFullscreen] = useState(false);

  const chartOptions = [
    {
      id: 'all' as ChartType,
      name: 'Alle Diagramme',
      icon: BarChart3,
      description: 'Vollständige Übersicht'
    },
    {
      id: 'progress' as ChartType,
      name: 'Trainingsfortschritt',
      icon: TrendingUp,
      description: 'Fortschritt über Zeit'
    },
    {
      id: 'exercises' as ChartType,
      name: 'Übungsabschluss',
      icon: Target,
      description: 'Abschlussraten nach Kategorie'
    },
    {
      id: 'assessment' as ChartType,
      name: 'Selbsteinschätzung',
      icon: Calendar,
      description: 'Bewertungen in verschiedenen Bereichen'
    }
  ];

  const handleExportData = () => {
    // Mock export functionality
    const data = {
      athlete: selectedAthlete?.displayName || 'Alle Athleten',
      dateRange,
      exportDate: new Date().toISOString(),
      charts: activeChart
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `snooker-data-${selectedAthlete?.displayName || 'alle-athleten'}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderChart = (type: ChartType) => {
    const chartHeight = isFullscreen ? 600 : 400;
    
    switch (type) {
      case 'progress':
        return (
          <TrainingProgressChart
            athleteId={selectedAthlete?.userId}
            dateRange={dateRange}
            height={chartHeight}
          />
        );
      case 'exercises':
        return (
          <ExerciseCompletionChart
            athleteId={selectedAthlete?.userId}
            dateRange={dateRange}
            height={chartHeight}
          />
        );
      case 'assessment':
        return (
          <SelfAssessmentChart
            athleteId={selectedAthlete?.userId}
            dateRange={dateRange}
            height={chartHeight}
          />
        );
      case 'all':
      default:
        return (
          <div className="space-y-6">
            <TrainingProgressChart
              athleteId={selectedAthlete?.userId}
              dateRange={dateRange}
              height={isFullscreen ? 500 : 350}
            />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <ExerciseCompletionChart
                athleteId={selectedAthlete?.userId}
                dateRange={dateRange}
                height={isFullscreen ? 450 : 350}
              />
              <SelfAssessmentChart
                athleteId={selectedAthlete?.userId}
                dateRange={dateRange}
                height={isFullscreen ? 450 : 350}
              />
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow ${isFullscreen ? 'fixed inset-4 z-50 overflow-auto' : ''}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Datenvisualisierung
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {selectedAthlete 
                ? `Daten für ${selectedAthlete.displayName}` 
                : 'Übersicht aller Athleten'
              }
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleExportData}
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Download className="h-4 w-4 mr-2" />
              Exportieren
            </button>
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {isFullscreen ? (
                <>
                  <Minimize2 className="h-4 w-4 mr-2" />
                  Verkleinern
                </>
              ) : (
                <>
                  <Maximize2 className="h-4 w-4 mr-2" />
                  Vollbild
                </>
              )}
            </button>
          </div>
        </div>

        {/* Chart Type Selector */}
        <div className="mt-4">
          <div className="flex flex-wrap gap-2">
            {chartOptions.map((option) => {
              const Icon = option.icon;
              return (
                <button
                  key={option.id}
                  onClick={() => setActiveChart(option.id)}
                  className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeChart === option.id
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  <div className="text-left">
                    <div>{option.name}</div>
                    <div className="text-xs opacity-75">{option.description}</div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      <div className="p-6">
        {renderChart(activeChart)}
      </div>

      {/* Data Summary */}
      {selectedAthlete && (
        <div className="px-6 pb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-gray-700 mb-3">
              Zusammenfassung für {selectedAthlete.displayName}
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Letzte Aktivität:</span>
                <div className="font-medium">Heute</div>
              </div>
              <div>
                <span className="text-gray-600">Trainingstage:</span>
                <div className="font-medium">24 von 30</div>
              </div>
              <div>
                <span className="text-gray-600">Durchschn. Fortschritt:</span>
                <div className="font-medium text-green-600">+12%</div>
              </div>
              <div>
                <span className="text-gray-600">Nächstes Ziel:</span>
                <div className="font-medium">85% Technik</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Fullscreen overlay */}
      {isFullscreen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsFullscreen(false)}
        />
      )}
    </div>
  );
};
