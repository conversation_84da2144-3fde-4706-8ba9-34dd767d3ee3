'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserConnections } from '@/lib/firestore';
import { 
  Users, 
  TrendingUp, 
  Calendar, 
  Target,
  Clock,
  Award
} from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'blue' | 'green' | 'yellow' | 'purple';
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  color = 'blue'
}) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    yellow: 'bg-yellow-50 text-yellow-600',
    purple: 'bg-purple-50 text-purple-600'
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
        <div className="ml-4 flex-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold text-gray-900">{value}</p>
              {subtitle && (
                <p className="text-sm text-gray-500">{subtitle}</p>
              )}
            </div>
            {trend && (
              <div className={`flex items-center text-sm ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUp className={`h-4 w-4 mr-1 ${
                  !trend.isPositive ? 'transform rotate-180' : ''
                }`} />
                {Math.abs(trend.value)}%
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export const OverviewStats: React.FC = () => {
  const { userProfile } = useAuth();
  const [stats, setStats] = useState({
    totalAthletes: 0,
    activeThisWeek: 0,
    totalSessions: 0,
    avgProgress: 0,
    loading: true
  });

  useEffect(() => {
    if (userProfile) {
      loadStats();
    }
  }, [userProfile]);

  const loadStats = async () => {
    if (!userProfile) return;

    try {
      const connections = await getUserConnections(userProfile.userId);
      const activeConnections = connections.filter(
        conn => conn.trainerId === userProfile.userId && conn.status === 'ACCEPTED'
      );

      // Mock statistics - in a real implementation, you would calculate these from actual data
      const mockStats = {
        totalAthletes: activeConnections.length,
        activeThisWeek: Math.floor(activeConnections.length * 0.8), // 80% active
        totalSessions: activeConnections.length * 12, // Average 12 sessions per athlete
        avgProgress: 78, // Mock average progress percentage
        loading: false
      };

      setStats(mockStats);
    } catch (error) {
      console.error('Error loading stats:', error);
      setStats(prev => ({ ...prev, loading: false }));
    }
  };

  if (stats.loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
                <div className="ml-4 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="Gesamt Athleten"
        value={stats.totalAthletes}
        subtitle="Verbundene Athleten"
        icon={Users}
        color="blue"
        trend={{
          value: 12,
          isPositive: true
        }}
      />
      
      <StatCard
        title="Aktiv diese Woche"
        value={stats.activeThisWeek}
        subtitle={`von ${stats.totalAthletes} Athleten`}
        icon={TrendingUp}
        color="green"
        trend={{
          value: 8,
          isPositive: true
        }}
      />
      
      <StatCard
        title="Training Sessions"
        value={stats.totalSessions}
        subtitle="Diesen Monat"
        icon={Calendar}
        color="purple"
        trend={{
          value: 15,
          isPositive: true
        }}
      />
      
      <StatCard
        title="Durchschn. Fortschritt"
        value={`${stats.avgProgress}%`}
        subtitle="Alle Athleten"
        icon={Target}
        color="yellow"
        trend={{
          value: 5,
          isPositive: true
        }}
      />
    </div>
  );
};
