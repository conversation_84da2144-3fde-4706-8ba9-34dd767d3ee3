'use client';

import React, { useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { MainLayout } from '@/components/layout/MainLayout';
import { OverviewStats } from '@/components/trainer/OverviewStats';
import { AthleteList } from '@/components/trainer/AthleteList';
import { DataFilters, FilterOptions } from '@/components/trainer/DataFilters';
import { DataVisualization } from '@/components/trainer/DataVisualization';
import { UserProfile } from '@/types';

export default function TrainerDashboard() {
  const [selectedAthlete, setSelectedAthlete] = useState<UserProfile | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({
    dateRange: { start: '', end: '' },
    athletes: [],
    dataTypes: [],
    categories: []
  });

  // Mock available athletes for filters
  const availableAthletes = [
    { id: 'athlete1', name: '<PERSON>' },
    { id: 'athlete2', name: '<PERSON>' },
    { id: 'athlete3', name: '<PERSON>' }
  ];

  return (
    <ProtectedRoute allowedRoles={['TRAINER', 'MENTAL_TRAINER']}>
      <MainLayout>
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Trainer Dashboard
            </h1>
            <p className="mt-2 text-gray-600">
              Übersicht über Ihre Athleten und deren Trainingsfortschritt
            </p>
          </div>

          {/* Overview Statistics */}
          <div className="mb-8">
            <OverviewStats />
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Athlete List */}
            <div className="lg:col-span-1">
              <AthleteList
                onAthleteSelect={setSelectedAthlete}
                selectedAthleteId={selectedAthlete?.userId}
              />
            </div>

            {/* Data Filters and Content */}
            <div className="lg:col-span-2 space-y-6">
              <DataFilters
                filters={filters}
                onFiltersChange={setFilters}
                availableAthletes={availableAthletes}
              />

              {/* Data Visualization */}
              <DataVisualization
                selectedAthlete={selectedAthlete}
                dateRange={filters.dateRange}
              />
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
