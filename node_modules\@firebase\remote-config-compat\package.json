{"name": "@firebase/remote-config-compat", "version": "0.2.19", "description": "The compatibility package of Remote Config", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm.js", "module": "dist/esm/index.esm.js", "exports": {".": {"types": "./dist/src/index.d.ts", "require": "./dist/index.cjs.js", "default": "./dist/esm/index.esm.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts'  --ignore-path '../../.gitignore'", "build": "rollup -c", "build:release": "yarn build && yarn add-compat-overloads", "build:deps": "lerna run --scope @firebase/remote-config-compat --include-dependencies build", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:all", "test:all": "run-p --npm-path npm test:browser", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:all", "test:browser": "karma start", "test:browser:debug": "karma start --browsers Chrome --auto-watch", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "add-compat-overloads": "ts-node-script ../../scripts/build/create-overloads.ts -i ../remote-config/dist/remote-config-public.d.ts -o dist/src/index.d.ts -a -r RemoteConfig:RemoteConfigCompat -r FirebaseApp:FirebaseAppCompat --moduleToEnhance @firebase/remote-config"}, "license": "Apache-2.0", "peerDependencies": {"@firebase/app-compat": "0.x"}, "dependencies": {"@firebase/remote-config": "0.6.6", "@firebase/remote-config-types": "0.4.0", "@firebase/util": "1.13.0", "@firebase/logger": "0.5.0", "@firebase/component": "0.7.0", "tslib": "^2.1.0"}, "devDependencies": {"rollup": "2.79.2", "@rollup/plugin-json": "6.1.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-typescript2": "0.36.0", "typescript": "5.5.4", "@firebase/app-compat": "0.5.0"}, "repository": {"directory": "packages/remote-config-compat", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "dist/src/index.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}}