'use client';

import React, { useEffect, useRef } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { formatChartDate, generateMockTrainingData } from '@/lib/utils';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface TrainingProgressChartProps {
  athleteId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  height?: number;
}

export const TrainingProgressChart: React.FC<TrainingProgressChartProps> = ({
  athleteId,
  dateRange,
  height = 400
}) => {
  const chartRef = useRef<ChartJS<'line'>>(null);

  // Generate mock data - in real implementation, fetch from Firestore
  const mockData = generateMockTrainingData(athleteId, dateRange);

  const data = {
    labels: mockData.dates.map(date => formatChartDate(date)),
    datasets: [
      {
        label: 'Trainingsfortschritt',
        data: mockData.progressScores,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      },
      {
        label: 'Selbsteinschätzung',
        data: mockData.selfAssessmentScores,
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointBackgroundColor: 'rgb(16, 185, 129)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      },
      {
        label: 'Übungsabschluss',
        data: mockData.exerciseCompletionRates,
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointBackgroundColor: 'rgb(245, 158, 11)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: athleteId ? `Trainingsfortschritt - Athlet ${athleteId.slice(-4)}` : 'Trainingsfortschritt - Alle Athleten',
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          bottom: 20
        }
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value}%`;
          }
        }
      }
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Datum',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Fortschritt (%)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        min: 0,
        max: 100,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    },
    elements: {
      point: {
        hoverBorderWidth: 3
      }
    }
  };

  useEffect(() => {
    const chart = chartRef.current;
    if (chart) {
      // Add any chart-specific event listeners or updates here
    }
  }, [athleteId, dateRange]);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div style={{ height: `${height}px` }}>
        <Line ref={chartRef} data={data} options={options} />
      </div>
      
      <div className="mt-4 grid grid-cols-3 gap-4 text-center">
        <div className="p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {mockData.progressScores[mockData.progressScores.length - 1]}%
          </div>
          <div className="text-sm text-gray-600">Aktueller Fortschritt</div>
        </div>
        <div className="p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {mockData.selfAssessmentScores[mockData.selfAssessmentScores.length - 1]}%
          </div>
          <div className="text-sm text-gray-600">Selbsteinschätzung</div>
        </div>
        <div className="p-3 bg-yellow-50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">
            {mockData.exerciseCompletionRates[mockData.exerciseCompletionRates.length - 1]}%
          </div>
          <div className="text-sm text-gray-600">Übungsabschluss</div>
        </div>
      </div>
    </div>
  );
};
