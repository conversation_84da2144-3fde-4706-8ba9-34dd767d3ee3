'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ExerciseCompletionChartProps {
  athleteId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  height?: number;
}

export const ExerciseCompletionChart: React.FC<ExerciseCompletionChartProps> = ({
  athleteId,
  dateRange,
  height = 400
}) => {
  // Mock data for exercise categories
  const exerciseCategories = [
    'Technik-Übungen',
    'Mental-Training',
    'Körperliche Fitness',
    'Taktik-Training',
    'Spielsituationen',
    'Konzentration'
  ];

  const mockData = {
    completed: [85, 92, 78, 88, 75, 90],
    total: [100, 100, 100, 100, 100, 100],
    inProgress: [10, 5, 15, 8, 20, 7],
    notStarted: [5, 3, 7, 4, 5, 3]
  };

  const data = {
    labels: exerciseCategories,
    datasets: [
      {
        label: 'Abgeschlossen',
        data: mockData.completed,
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      },
      {
        label: 'In Bearbeitung',
        data: mockData.inProgress,
        backgroundColor: 'rgba(245, 158, 11, 0.8)',
        borderColor: 'rgb(245, 158, 11)',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      },
      {
        label: 'Nicht begonnen',
        data: mockData.notStarted,
        backgroundColor: 'rgba(156, 163, 175, 0.8)',
        borderColor: 'rgb(156, 163, 175)',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: athleteId ? `Übungsabschluss - Athlet ${athleteId.slice(-4)}` : 'Übungsabschluss - Alle Athleten',
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          bottom: 20
        }
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value}%`;
          },
          footer: function(tooltipItems: any[]) {
            const categoryIndex = tooltipItems[0].dataIndex;
            const total = mockData.completed[categoryIndex] + 
                         mockData.inProgress[categoryIndex] + 
                         mockData.notStarted[categoryIndex];
            return `Gesamt: ${total}%`;
          }
        }
      }
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Übungskategorien',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          display: false
        },
        ticks: {
          maxRotation: 45,
          minRotation: 0
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Prozentsatz (%)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        min: 0,
        max: 100,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    },
    elements: {
      bar: {
        borderWidth: 1
      }
    }
  };

  // Calculate overall completion rate
  const totalCompleted = mockData.completed.reduce((sum, val) => sum + val, 0);
  const totalExercises = mockData.completed.length * 100;
  const overallCompletionRate = Math.round((totalCompleted / totalExercises) * 100);

  const totalInProgress = mockData.inProgress.reduce((sum, val) => sum + val, 0);
  const overallInProgressRate = Math.round((totalInProgress / totalExercises) * 100);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div style={{ height: `${height}px` }}>
        <Bar data={data} options={options} />
      </div>
      
      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {overallCompletionRate}%
          </div>
          <div className="text-sm text-gray-600">Gesamt abgeschlossen</div>
        </div>
        <div className="text-center p-3 bg-yellow-50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">
            {overallInProgressRate}%
          </div>
          <div className="text-sm text-gray-600">In Bearbeitung</div>
        </div>
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {exerciseCategories.length}
          </div>
          <div className="text-sm text-gray-600">Kategorien</div>
        </div>
        <div className="text-center p-3 bg-purple-50 rounded-lg">
          <div className="text-2xl font-bold text-purple-600">
            {Math.max(...mockData.completed)}%
          </div>
          <div className="text-sm text-gray-600">Beste Kategorie</div>
        </div>
      </div>
    </div>
  );
};
