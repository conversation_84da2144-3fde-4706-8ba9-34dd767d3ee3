'use client';

import React, { useState } from 'react';
import { 
  Filter, 
  Calendar, 
  User, 
  BarChart3, 
  Target,
  ChevronDown,
  X
} from 'lucide-react';

export interface FilterOptions {
  dateRange: {
    start: string;
    end: string;
  };
  athletes: string[];
  dataTypes: string[];
  categories: string[];
}

interface DataFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  availableAthletes: Array<{ id: string; name: string }>;
}

const dataTypeOptions = [
  { id: 'training', label: 'Trainingsdaten', icon: BarChart3 },
  { id: 'exercises', label: 'Übungen', icon: Target },
  { id: 'assessments', label: 'Selbsteinschätzungen', icon: User },
  { id: 'plans', label: 'Trainingspläne', icon: Calendar }
];

const categoryOptions = [
  { id: 'technique', label: 'Technik' },
  { id: 'mental', label: 'Mental' },
  { id: 'physical', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { id: 'tactical', label: 'Taktik' }
];

export const DataFilters: React.FC<DataFiltersProps> = ({
  filters,
  onFiltersChange,
  availableAthletes
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const updateFilters = (updates: Partial<FilterOptions>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const clearFilters = () => {
    onFiltersChange({
      dateRange: { start: '', end: '' },
      athletes: [],
      dataTypes: [],
      categories: []
    });
  };

  const hasActiveFilters = 
    filters.dateRange.start || 
    filters.dateRange.end || 
    filters.athletes.length > 0 || 
    filters.dataTypes.length > 0 || 
    filters.categories.length > 0;

  const toggleDataType = (typeId: string) => {
    const newTypes = filters.dataTypes.includes(typeId)
      ? filters.dataTypes.filter(id => id !== typeId)
      : [...filters.dataTypes, typeId];
    updateFilters({ dataTypes: newTypes });
  };

  const toggleCategory = (categoryId: string) => {
    const newCategories = filters.categories.includes(categoryId)
      ? filters.categories.filter(id => id !== categoryId)
      : [...filters.categories, categoryId];
    updateFilters({ categories: newCategories });
  };

  const toggleAthlete = (athleteId: string) => {
    const newAthletes = filters.athletes.includes(athleteId)
      ? filters.athletes.filter(id => id !== athleteId)
      : [...filters.athletes, athleteId];
    updateFilters({ athletes: newAthletes });
  };

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-gray-500" />
            <h3 className="text-lg font-medium text-gray-900">Filter</h3>
            {hasActiveFilters && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Aktiv
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-red-600 transition-colors"
              >
                <X className="h-4 w-4 mr-1" />
                Zurücksetzen
              </button>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="p-4 space-y-6">
          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Zeitraum
            </label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Von</label>
                <input
                  type="date"
                  value={filters.dateRange.start}
                  onChange={(e) => updateFilters({
                    dateRange: { ...filters.dateRange, start: e.target.value }
                  })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Bis</label>
                <input
                  type="date"
                  value={filters.dateRange.end}
                  onChange={(e) => updateFilters({
                    dateRange: { ...filters.dateRange, end: e.target.value }
                  })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Athletes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Athleten
            </label>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {availableAthletes.map((athlete) => (
                <label key={athlete.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.athletes.includes(athlete.id)}
                    onChange={() => toggleAthlete(athlete.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{athlete.name}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Data Types */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Datentypen
            </label>
            <div className="grid grid-cols-2 gap-2">
              {dataTypeOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <label key={option.id} className="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.dataTypes.includes(option.id)}
                      onChange={() => toggleDataType(option.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <Icon className="h-4 w-4 ml-2 text-gray-500" />
                    <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                  </label>
                );
              })}
            </div>
          </div>

          {/* Categories */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Kategorien
            </label>
            <div className="flex flex-wrap gap-2">
              {categoryOptions.map((category) => (
                <button
                  key={category.id}
                  onClick={() => toggleCategory(category.id)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    filters.categories.includes(category.id)
                      ? 'bg-blue-100 text-blue-800 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  {category.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
